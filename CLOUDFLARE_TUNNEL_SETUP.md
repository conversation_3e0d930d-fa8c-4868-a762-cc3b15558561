# Cloudflare Tunnel Setup for WorkSuite SAAS

## ✅ Setup Complete!

The Cloudflare tunnel has been successfully configured and is now running as a system service on your EC2 instance.

## 🌐 Public Access

Your WorkSuite SAAS application is now publicly accessible at:

**🔗 https://erp.iti.id.vn**

## 📋 Configuration Details

### Tunnel Information
- **Public Hostname**: `erp.iti.id.vn`
- **Local Service**: `https://localhost:8643` (HTTPS)
- **Protocol**: QUIC (modern, fast tunnel protocol)
- **SSL Verification**: Disabled (using self-signed certificates)
- **HTTP/2 Origin**: Enabled

### Service Configuration
- **Service Name**: `cloudflared.service`
- **Service Status**: ✅ Active and Running
- **Auto-start**: ✅ Enabled (starts automatically on boot)
- **Restart Policy**: On failure with 5-second delay
- **User**: root

### Files Created
```
/usr/local/bin/cloudflared              # Cloudflared binary
/etc/cloudflared/config.yml             # Tunnel configuration
/etc/cloudflared/tunnel-token.txt       # Tunnel credentials
/etc/systemd/system/cloudflared.service # SystemD service file
```

## 🔧 Service Management

### Check Service Status
```bash
sudo systemctl status cloudflared
```

### View Service Logs
```bash
sudo journalctl -u cloudflared -f
```

### Restart Service
```bash
sudo systemctl restart cloudflared
```

### Stop/Start Service
```bash
sudo systemctl stop cloudflared
sudo systemctl start cloudflared
```

### Disable/Enable Auto-start
```bash
sudo systemctl disable cloudflared  # Disable auto-start
sudo systemctl enable cloudflared   # Enable auto-start
```

## 📊 Tunnel Status

### Current Status
- **Tunnel Connections**: 4 active connections to Cloudflare edge servers
- **Edge Locations**: Singapore (sin06, sin11, sin12, sin20)
- **Connection Protocol**: QUIC
- **Configuration Version**: 1
- **Metrics Server**: Running on 127.0.0.1:20241/metrics

### Connection Details
The tunnel maintains multiple redundant connections to Cloudflare's edge network for high availability and performance.

## 🌍 How It Works

1. **Client Request**: User accesses `https://erp.iti.id.vn`
2. **Cloudflare Edge**: Request hits Cloudflare's global network
3. **Tunnel Connection**: Cloudflare forwards request through secure tunnel
4. **Local Service**: Request reaches your application at `https://localhost:8643`
5. **Response**: Application response travels back through the same path

## 🔒 Security Features

- **End-to-End Encryption**: All traffic encrypted between client and Cloudflare
- **DDoS Protection**: Cloudflare's built-in DDoS protection
- **SSL/TLS**: Automatic SSL certificate management by Cloudflare
- **Origin Protection**: Your server's real IP address is hidden
- **Access Control**: Can be configured with Cloudflare Access policies

## 📈 Performance Benefits

- **Global CDN**: Content delivered from nearest Cloudflare edge server
- **HTTP/2 & HTTP/3**: Modern protocols for faster loading
- **Compression**: Automatic content compression
- **Caching**: Static assets cached at edge locations
- **Load Balancing**: Automatic failover between tunnel connections

## 🛠️ Troubleshooting

### Common Issues

#### Tunnel Not Connecting
```bash
# Check service status
sudo systemctl status cloudflared

# Check logs for errors
sudo journalctl -u cloudflared -n 50

# Restart service
sudo systemctl restart cloudflared
```

#### Website Not Accessible
1. **Check tunnel status**: Ensure service is running
2. **Check local service**: Verify `https://localhost:8643` works locally
3. **Check DNS**: Ensure `erp.iti.id.vn` resolves to Cloudflare IPs
4. **Check Cloudflare dashboard**: Verify tunnel status in Cloudflare dashboard

#### Performance Issues
```bash
# Check tunnel metrics
curl http://127.0.0.1:20241/metrics

# Monitor tunnel logs
sudo journalctl -u cloudflared -f

# Check system resources
htop
```

### Log Analysis
```bash
# Recent tunnel activity
sudo journalctl -u cloudflared --since "1 hour ago"

# Filter for errors
sudo journalctl -u cloudflared | grep -i error

# Filter for connection events
sudo journalctl -u cloudflared | grep -i "registered tunnel"
```

## 🔄 Maintenance

### Regular Checks
- Monitor service status weekly
- Check tunnel logs for any errors
- Verify public accessibility monthly
- Update cloudflared binary quarterly

### Updates
```bash
# Download latest cloudflared
sudo wget -O /usr/local/bin/cloudflared https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64
sudo chmod +x /usr/local/bin/cloudflared

# Restart service to use new version
sudo systemctl restart cloudflared
```

## 📝 Configuration Files

### Tunnel Configuration (`/etc/cloudflared/config.yml`)
```yaml
ingress:
  - hostname: erp.iti.id.vn
    service: https://localhost:8643
    originRequest:
      noTLSVerify: true
      http2Origin: true
  - service: http_status:404
```

### SystemD Service (`/etc/systemd/system/cloudflared.service`)
```ini
[Unit]
Description=Cloudflare Tunnel
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/cloudflared tunnel --config /etc/cloudflared/config.yml run --token [TOKEN]
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
```

## 🎯 Next Steps

1. **Test Application**: Access `https://erp.iti.id.vn` and complete WorkSuite setup
2. **Monitor Performance**: Check application performance through the tunnel
3. **Configure Cloudflare**: Set up additional Cloudflare features (WAF, caching rules, etc.)
4. **SSL Optimization**: Consider upgrading to trusted SSL certificates for the origin
5. **Access Policies**: Configure Cloudflare Access for additional security if needed

## 📞 Support

- **Cloudflare Documentation**: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/
- **Tunnel Logs**: `sudo journalctl -u cloudflared`
- **Service Status**: `sudo systemctl status cloudflared`
- **Configuration**: `/etc/cloudflared/config.yml`

---

**🎉 Congratulations!** Your WorkSuite SAAS application is now publicly accessible through Cloudflare's secure tunnel at **https://erp.iti.id.vn**
