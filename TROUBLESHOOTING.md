# WorkSuite SAAS Deployment Troubleshooting Guide

## Common Issues and Solutions

### 1. SSH Connection Issues

#### Problem: Cannot connect to EC2 instance
```bash
ssh: connect to host ec2-xxx.amazonaws.com port 22: Connection timed out
```

**Solutions:**
1. **Check Security Group**: Ensure SSH (port 22) is allowed from your IP
2. **Verify Key Permissions**: `chmod 400 your-key.pem`
3. **Check Instance Status**: Ensure EC2 instance is running
4. **Network Connectivity**: Test with `ping` or `telnet`

```bash
# Test connectivity
ping ec2-52-77-233-254.ap-southeast-1.compute.amazonaws.com
telnet ec2-52-77-233-254.ap-southeast-1.compute.amazonaws.com 22

# Correct key permissions
chmod 400 thaodoiti.pem

# Verbose SSH for debugging
ssh -v -i thaodoiti.pem <EMAIL>
```

### 2. Ansible Execution Issues

#### Problem: Ansible playbook fails with permission errors
```
FAILED! => {"msg": "Failed to connect to the host via ssh"}
```

**Solutions:**
1. **Update Inventory**: Ensure correct SSH key path in `inventory/hosts.yml`
2. **Test SSH**: Verify manual SSH connection works
3. **Check Ansible Configuration**: Review `ansible.cfg` settings

```bash
# Test Ansible connectivity
ansible -i inventory/hosts.yml worksuite-production -m ping

# Run with verbose output
ansible-playbook -i inventory/hosts.yml infrastructure.yml -vvv
```

#### Problem: Package installation fails
```
E: Unable to locate package php8.2-fpm
```

**Solutions:**
1. **Update Package Cache**: Run `apt update` first
2. **Add PHP Repository**: Ensure ondrej/php PPA is added
3. **Check Ubuntu Version**: Verify compatibility

```bash
# Manual fix on server
sudo apt update
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install php8.2-fpm
```

### 3. Service Startup Issues

#### Problem: PHP-FPM fails to start
```
Job for php8.2-fpm.service failed because the control process exited with error code
```

**Solutions:**
1. **Check Configuration**: Verify PHP-FPM pool configuration
2. **Check Permissions**: Ensure directories exist and have correct permissions
3. **Review Logs**: Check systemd and PHP-FPM logs

```bash
# Check service status
sudo systemctl status php8.2-fpm

# View detailed logs
sudo journalctl -u php8.2-fpm -n 50

# Test configuration
sudo php-fpm8.2 -t

# Check socket permissions
ls -la /run/php/
```

#### Problem: MariaDB authentication issues
```
ERROR 1045 (28000): Access denied for user 'root'@'localhost'
```

**Solutions:**
1. **Reset Root Password**: Use mysql_secure_installation
2. **Check Authentication Plugin**: Verify auth_socket vs mysql_native_password
3. **Use Sudo**: Access as system mysql user

```bash
# Access MariaDB as system user
sudo mysql -u root

# Reset root password
sudo mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';"

# Check user authentication
sudo mysql -u root -e "SELECT User, Host, plugin FROM mysql.user WHERE User='root';"
```

### 4. Network Connectivity Issues

#### Problem: Cannot access application on ports 8601/8643
```
curl: (7) Failed to connect to server port 8601: Connection refused
```

**Solutions:**
1. **Check AWS Security Group**: Add inbound rules for ports 8601 and 8643
2. **Verify Nginx Status**: Ensure Nginx is running and listening
3. **Check UFW Firewall**: Verify local firewall rules

```bash
# Check if Nginx is listening
sudo ss -tlnp | grep -E ':(8601|8643)'

# Check Nginx status
sudo systemctl status nginx

# Test locally on server
curl -I http://localhost:8601

# Check UFW status
sudo ufw status

# Add firewall rules if needed
sudo ufw allow 8601/tcp
sudo ufw allow 8643/tcp
```

#### Problem: SSL certificate issues
```
SSL_ERROR_SELF_SIGNED_CERT
```

**Solutions:**
1. **Accept Self-Signed Certificate**: Normal for development/testing
2. **Install Trusted Certificate**: For production use
3. **Check Certificate Details**: Verify certificate is properly generated

```bash
# Check SSL certificate
/usr/local/bin/ssl-info.sh

# Test SSL connection
openssl s_client -connect localhost:8643

# Regenerate certificate if needed
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/worksuite.key \
  -out /etc/ssl/certs/worksuite.crt
```

### 5. Application Issues

#### Problem: Laravel shows 500 Internal Server Error
```
HTTP/1.1 500 Internal Server Error
```

**Solutions:**
1. **Check Application Logs**: Review Laravel logs for specific errors
2. **Verify Environment**: Ensure `.env` file is properly configured
3. **Check Permissions**: Verify storage and cache directory permissions
4. **Run Migrations**: Ensure database is properly set up

```bash
# Check Laravel logs
sudo tail -f /var/www/worksuite/storage/logs/laravel.log

# Check environment configuration
sudo -u worksuite cat /var/www/worksuite/.env

# Fix permissions
sudo chown -R worksuite:www-data /var/www/worksuite/storage
sudo chmod -R 775 /var/www/worksuite/storage

# Clear caches
cd /var/www/worksuite
sudo -u worksuite php artisan cache:clear
sudo -u worksuite php artisan config:clear
sudo -u worksuite php artisan view:clear

# Check database connection
sudo -u worksuite php artisan migrate:status
```

#### Problem: Database connection errors
```
SQLSTATE[HY000] [2002] Connection refused
```

**Solutions:**
1. **Check MariaDB Status**: Ensure database service is running
2. **Verify Credentials**: Check database username/password in `.env`
3. **Test Connection**: Use mysql client to test connectivity

```bash
# Check MariaDB status
sudo systemctl status mariadb

# Test database connection
mysql -u worksuite_user -p worksuite_saas

# Check database configuration
sudo mysql -u root -p -e "SHOW DATABASES;"
sudo mysql -u root -p -e "SELECT User, Host FROM mysql.user;"

# Restart MariaDB if needed
sudo systemctl restart mariadb
```

### 6. Performance Issues

#### Problem: Slow application response
**Solutions:**
1. **Check System Resources**: Monitor CPU, memory, and disk usage
2. **Optimize PHP-FPM**: Adjust worker processes
3. **Enable OPcache**: Verify PHP OPcache is working
4. **Database Optimization**: Check slow queries

```bash
# Monitor system resources
htop
free -h
df -h
iostat 1

# Check PHP-FPM status
curl http://localhost:8601/fpm-status

# Check OPcache status
php -i | grep opcache

# Monitor database
sudo mysql -u root -p -e "SHOW PROCESSLIST;"
sudo mysql -u root -p -e "SHOW STATUS LIKE 'Slow_queries';"
```

### 7. Backup and Recovery Issues

#### Problem: Backup script fails
```
mysqldump: Error: 'Access denied for user'
```

**Solutions:**
1. **Check Database Credentials**: Verify backup script has correct credentials
2. **Test Manual Backup**: Run mysqldump manually
3. **Check Disk Space**: Ensure sufficient space for backups

```bash
# Test manual backup
mysqldump -u worksuite_user -p worksuite_saas > test_backup.sql

# Check backup script
sudo cat /usr/local/bin/backup-worksuite.sh

# Run backup manually
sudo /usr/local/bin/backup-worksuite.sh

# Check backup directory
ls -la /var/backups/worksuite/

# Check disk space
df -h /var/backups/
```

## Diagnostic Commands

### System Health Check
```bash
# Quick system overview
sudo systemctl status nginx mariadb redis-server php8.2-fpm laravel-queue

# Check all listening ports
sudo ss -tlnp

# Check disk usage
df -h

# Check memory usage
free -h

# Check system load
uptime
```

### Application Health Check
```bash
# Laravel version and status
cd /var/www/worksuite
sudo -u worksuite php artisan --version
sudo -u worksuite php artisan migrate:status

# Check queue status
sudo -u worksuite php artisan queue:work --once

# Test database connection
sudo -u worksuite php artisan tinker --execute="DB::connection()->getPdo();"
```

### Log Analysis
```bash
# Recent application errors
sudo tail -100 /var/www/worksuite/storage/logs/laravel.log | grep ERROR

# Recent Nginx errors
sudo tail -100 /var/log/nginx/worksuite-error.log

# Recent system errors
sudo journalctl --since "1 hour ago" --priority=err

# PHP-FPM errors
sudo journalctl -u php8.2-fpm --since "1 hour ago"
```

## Recovery Procedures

### Service Recovery
```bash
# Restart all services
sudo systemctl restart nginx mariadb redis-server php8.2-fpm laravel-queue

# Reload Nginx configuration
sudo nginx -t && sudo systemctl reload nginx

# Reset application caches
cd /var/www/worksuite
sudo -u worksuite php artisan cache:clear
sudo -u worksuite php artisan config:cache
sudo -u worksuite php artisan route:cache
```

### Database Recovery
```bash
# Restore from backup
mysql -u worksuite_user -p worksuite_saas < /var/backups/worksuite/database_YYYYMMDD_HHMMSS.sql.gz

# Repair tables if needed
sudo mysql -u root -p -e "REPAIR TABLE worksuite_saas.table_name;"

# Check database integrity
sudo mysql -u root -p -e "CHECK TABLE worksuite_saas.table_name;"
```

### Complete Redeployment
```bash
# If all else fails, redeploy
ansible-playbook -i inventory/hosts.yml site.yml --tags application

# Or specific components
ansible-playbook -i inventory/hosts.yml infrastructure.yml
ansible-playbook -i inventory/hosts.yml application.yml
```

## Getting Help

### Log Collection for Support
```bash
# Collect system information
sudo journalctl --since "24 hours ago" > system_logs.txt
sudo tail -1000 /var/www/worksuite/storage/logs/laravel.log > app_logs.txt
sudo nginx -T > nginx_config.txt
sudo systemctl status --all > service_status.txt

# Create support bundle
tar -czf support_bundle.tar.gz system_logs.txt app_logs.txt nginx_config.txt service_status.txt
```

### Verification Script
Run the verification playbook to get a comprehensive status report:
```bash
ansible-playbook -i inventory/hosts.yml verify.yml
```

This will generate a detailed report at `/tmp/worksuite-verification-report.txt` on the target server.
