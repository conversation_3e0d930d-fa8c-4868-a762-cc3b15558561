---
- name: Install and Configure Infrastructure for WorkSuite SAAS
  hosts: worksuite_servers
  become: yes
  gather_facts: yes
  
  vars:
    php_fpm_service: "php{{ php_version }}-fpm"
    php_fpm_socket: "/run/php/php{{ php_version }}-fpm.sock"
    
  tasks:
    - name: Update apt cache
      apt:
        update_cache: yes
        cache_valid_time: 3600
      tags: [system, update]

    - name: Install system packages
      apt:
        name: "{{ system_packages }}"
        state: present
      tags: [system, packages]

    - name: Set system timezone
      timezone:
        name: "{{ system_timezone }}"
      tags: [system, timezone]

    - name: Create swap file if not exists
      block:
        - name: Check if swap file exists
          stat:
            path: "{{ swap_file_path }}"
          register: swap_file_check

        - name: Create swap file
          command: fallocate -l {{ swap_file_size }} {{ swap_file_path }}
          when: not swap_file_check.stat.exists

        - name: Set swap file permissions
          file:
            path: "{{ swap_file_path }}"
            mode: '0600'
          when: not swap_file_check.stat.exists

        - name: Make swap file
          command: mkswap {{ swap_file_path }}
          when: not swap_file_check.stat.exists

        - name: Enable swap file
          command: swapon {{ swap_file_path }}
          when: not swap_file_check.stat.exists

        - name: Add swap to fstab
          lineinfile:
            path: /etc/fstab
            line: "{{ swap_file_path }} none swap sw 0 0"
            state: present
          when: not swap_file_check.stat.exists
      tags: [system, swap]

    # Create application user first
    - name: Create application user
      user:
        name: "{{ app_user }}"
        group: "{{ app_group }}"
        shell: /bin/bash
        home: "/home/<USER>"
        create_home: yes
        system: no
      tags: [system, user]

    - name: Create basic application directories
      file:
        path: "{{ item }}"
        state: directory
        owner: "{{ app_user }}"
        group: "{{ app_group }}"
        mode: '0755'
      loop:
        - "{{ app_root }}"
        - "{{ app_storage }}"
        - "{{ app_bootstrap_cache }}"
      tags: [system, directories]

    # PHP Installation and Configuration
    - name: Add PHP repository
      apt_repository:
        repo: "ppa:ondrej/php"
        state: present
      tags: [php, repository]

    - name: Install PHP packages
      apt:
        name: "{{ php_packages }}"
        state: present
        update_cache: yes
      tags: [php, packages]

    - name: Configure PHP-FPM pool
      template:
        src: templates/php-fpm-pool.conf.j2
        dest: "/etc/php/{{ php_version }}/fpm/pool.d/worksuite.conf"
        backup: yes
      notify: restart php-fpm
      tags: [php, config]

    - name: Configure PHP settings
      template:
        src: templates/php.ini.j2
        dest: "/etc/php/{{ php_version }}/fpm/conf.d/99-worksuite.ini"
        backup: yes
      notify: restart php-fpm
      tags: [php, config]

    - name: Configure PHP CLI settings
      template:
        src: templates/php.ini.j2
        dest: "/etc/php/{{ php_version }}/cli/conf.d/99-worksuite.ini"
        backup: yes
      tags: [php, config]

    - name: Start and enable PHP-FPM
      systemd:
        name: "{{ php_fpm_service }}"
        state: started
        enabled: yes
      tags: [php, service]

    # MariaDB Installation and Configuration
    - name: Install MariaDB
      apt:
        name: "{{ mariadb_packages }}"
        state: present
      tags: [mariadb, packages]

    - name: Start and enable MariaDB
      systemd:
        name: mariadb
        state: started
        enabled: yes
      tags: [mariadb, service]

    - name: Set MariaDB root password
      shell: |
        sudo -u mysql mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '{{ mysql_root_password }}';"
        sudo -u mysql mysql -e "FLUSH PRIVILEGES;"
      tags: [mariadb, security]

    - name: Create MariaDB configuration
      template:
        src: templates/mariadb.cnf.j2
        dest: /etc/mysql/mariadb.conf.d/99-worksuite.cnf
        backup: yes
      notify: restart mariadb
      tags: [mariadb, config]

    - name: Create application database
      mysql_db:
        name: "{{ mysql_database }}"
        state: present
        login_user: root
        login_password: "{{ mysql_root_password }}"
      tags: [mariadb, database]

    - name: Create application database user
      mysql_user:
        name: "{{ mysql_user }}"
        password: "{{ mysql_password }}"
        priv: "{{ mysql_database }}.*:ALL"
        host: localhost
        state: present
        login_user: root
        login_password: "{{ mysql_root_password }}"
      tags: [mariadb, user]

    # Redis Installation and Configuration
    - name: Install Redis
      apt:
        name: "{{ redis_packages }}"
        state: present
      tags: [redis, packages]

    - name: Configure Redis
      template:
        src: templates/redis.conf.j2
        dest: /etc/redis/redis.conf
        backup: yes
      notify: restart redis
      tags: [redis, config]

    - name: Start and enable Redis
      systemd:
        name: redis-server
        state: started
        enabled: yes
      tags: [redis, service]

    # Nginx Installation and Configuration
    - name: Install Nginx
      apt:
        name: "{{ nginx_packages }}"
        state: present
      tags: [nginx, packages]

    - name: Remove default Nginx site
      file:
        path: /etc/nginx/sites-enabled/default
        state: absent
      notify: restart nginx
      tags: [nginx, config]

    - name: Start and enable Nginx
      systemd:
        name: nginx
        state: started
        enabled: yes
      tags: [nginx, service]

    # Composer Installation
    - name: Download Composer installer
      get_url:
        url: "{{ composer_url }}"
        dest: /tmp/composer-setup.php
      tags: [composer, install]

    - name: Install Composer
      command: php /tmp/composer-setup.php --install-dir=/usr/local/bin --filename=composer
      args:
        creates: "{{ composer_path }}"
      tags: [composer, install]

    - name: Make Composer executable
      file:
        path: "{{ composer_path }}"
        mode: '0755'
      tags: [composer, install]



    # Firewall Configuration
    - name: Configure UFW firewall
      block:
        - name: Reset UFW to defaults
          ufw:
            state: reset

        - name: Allow SSH
          ufw:
            rule: allow
            port: '22'
            proto: tcp

        - name: Allow HTTP port
          ufw:
            rule: allow
            port: "{{ nginx_http_port }}"
            proto: tcp

        - name: Allow HTTPS port
          ufw:
            rule: allow
            port: "{{ nginx_https_port }}"
            proto: tcp

        - name: Enable UFW
          ufw:
            state: enabled
            policy: deny
            direction: incoming
      when: ufw_enabled | default(false)
      tags: [security, firewall]

  handlers:
    - name: restart php-fpm
      systemd:
        name: "{{ php_fpm_service }}"
        state: restarted

    - name: restart mariadb
      systemd:
        name: mariadb
        state: restarted

    - name: restart redis
      systemd:
        name: redis-server
        state: restarted

    - name: restart nginx
      systemd:
        name: nginx
        state: restarted
