---
- name: Disable License Check for WorkSuite SAAS
  hosts: worksuite-production
  become: yes
  vars:
    app_path: /var/www/worksuite

  tasks:
    - name: Create backup of original files
      copy:
        src: "{{ item }}"
        dest: "{{ item }}.backup"
        remote_src: yes
      loop:
        - "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php"
        - "{{ app_path }}/vendor/froiden/envato/src/Helpers/FroidenApp.php"
        - "{{ app_path }}/app/Http/Middleware/CheckCompanyPackage.php"
      ignore_errors: yes

    - name: Modify AppBoot.php to always return true for isLegal()
      replace:
        path: "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php"
        regexp: 'public function isLegal\(\)\s*\{[\s\S]*?return false;\s*\}'
        replace: |
          public function isLegal()
          {
              return true;
          }

    - name: Modify AppBoot.php verifyPurchase method to always return success
      replace:
        path: "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php"
        regexp: 'public function verifyPurchase\(\)\s*\{[\s\S]*?return view\([^}]+\}[\s\S]*?\}'
        replace: |
          public function verifyPurchase()
          {
              return redirect()->route('dashboard');
          }

    - name: Modify FroidenApp.php to always return true for isLocalHost()
      replace:
        path: "{{ app_path }}/vendor/froiden/envato/src/Helpers/FroidenApp.php"
        regexp: 'public static function isLocalHost\(\)\s*\{[\s\S]*?return false;\s*\}'
        replace: |
          public static function isLocalHost()
          {
              return true;
          }

    - name: Modify CheckCompanyPackage middleware to always allow access
      replace:
        path: "{{ app_path }}/app/Http/Middleware/CheckCompanyPackage.php"
        regexp: 'public function handle\(Request \$request, Closure \$next\): Response\s*\{[\s\S]*?return \$next\(\$request\);\s*\}'
        replace: |
          public function handle(Request $request, Closure $next): Response
          {
              return $next($request);
          }

    - name: Check if checkCompanyPackageIsValid function exists in Helper
      shell: grep -n "function checkCompanyPackageIsValid" {{ app_path }}/app/Helper/start.php
      register: helper_function_check
      ignore_errors: yes

    - name: Modify checkCompanyPackageIsValid function to always return true
      replace:
        path: "{{ app_path }}/app/Helper/start.php"
        regexp: 'function checkCompanyPackageIsValid\([^)]*\)\s*\{[\s\S]*?return false;\s*\}'
        replace: |
          function checkCompanyPackageIsValid($companyId)
          {
              return true;
          }
      when: helper_function_check.rc == 0

    - name: Alternative - Add checkCompanyPackageIsValid function if not exists
      lineinfile:
        path: "{{ app_path }}/app/Helper/start.php"
        line: |
          if (!function_exists('checkCompanyPackageIsValid')) {
              function checkCompanyPackageIsValid($companyId) {
                  return true;
              }
          }
        insertbefore: EOF
      when: helper_function_check.rc != 0

    - name: Check if checkCompanyCanAddMoreEmployees function exists
      shell: grep -n "function checkCompanyCanAddMoreEmployees" {{ app_path }}/app/Helper/start.php
      register: employee_function_check
      ignore_errors: yes

    - name: Modify checkCompanyCanAddMoreEmployees function to always return true
      replace:
        path: "{{ app_path }}/app/Helper/start.php"
        regexp: 'function checkCompanyCanAddMoreEmployees\([^)]*\)\s*\{[\s\S]*?return false;\s*\}'
        replace: |
          function checkCompanyCanAddMoreEmployees($companyId)
          {
              return true;
          }
      when: employee_function_check.rc == 0

    - name: Alternative - Add checkCompanyCanAddMoreEmployees function if not exists
      lineinfile:
        path: "{{ app_path }}/app/Helper/start.php"
        line: |
          if (!function_exists('checkCompanyCanAddMoreEmployees')) {
              function checkCompanyCanAddMoreEmployees($companyId) {
                  return true;
              }
          }
        insertbefore: EOF
      when: employee_function_check.rc != 0

    - name: Create custom route to bypass verify-purchase
      lineinfile:
        path: "{{ app_path }}/routes/web.php"
        line: |
          // Custom route to bypass license verification
          Route::get('verify-purchase', function() {
              return redirect()->route('dashboard');
          })->name('verify-purchase-bypass');
        insertafter: "<?php"

    - name: Clear application cache
      shell: |
        cd {{ app_path }}
        sudo -u worksuite php artisan cache:clear
        sudo -u worksuite php artisan config:clear
        sudo -u worksuite php artisan route:clear
        sudo -u worksuite php artisan view:clear

    - name: Restart PHP-FPM to apply changes
      systemd:
        name: php8.2-fpm
        state: restarted

    - name: Test application access
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 30
      register: app_test
      ignore_errors: yes

    - name: Display results
      debug:
        msg: |
          License check has been disabled!
          
          Changes made:
          - Modified AppBoot.php isLegal() to always return true
          - Modified verifyPurchase() to redirect to dashboard
          - Modified FroidenApp.php isLocalHost() to always return true
          - Modified CheckCompanyPackage middleware to always allow access
          - Modified helper functions to always return true
          - Added bypass route for verify-purchase
          
          Application Status: {{ 'SUCCESS' if app_test.status == 200 or app_test.status == 302 else 'FAILED' }}
          
          You can now access the application without license verification!
          Public URL: https://erp.iti.id.vn
