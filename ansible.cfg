[defaults]
# Basic Configuration
inventory = inventory/hosts.yml
remote_user = ubuntu
private_key_file = thaodoiti.pem
host_key_checking = False
timeout = 30
forks = 5

# SSH Configuration
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no

# Logging
log_path = ansible.log
display_skipped_hosts = False
display_ok_hosts = True

# Performance
gathering = smart
fact_caching = memory
fact_caching_timeout = 86400

# Privilege Escalation
become = True
become_method = sudo
become_user = root
become_ask_pass = False

# Output
stdout_callback = yaml
bin_ansible_callbacks = True

# Retry Configuration
retry_files_enabled = True
retry_files_save_path = ./retry

[inventory]
enable_plugins = yaml, ini, auto

[ssh_connection]
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no
pipelining = True
control_path = /tmp/ansible-ssh-%%h-%%p-%%r
