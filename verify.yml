---
- name: Verify WorkSuite SAAS Deployment
  hosts: worksuite_servers
  become: yes
  gather_facts: yes
  
  vars:
    php_fpm_service: "php{{ php_version }}-fpm"
    
  tasks:
    - name: Check system services status
      systemd:
        name: "{{ item }}"
      register: service_status
      loop:
        - nginx
        - mariadb
        - redis-server
        - "{{ php_fpm_service }}"
        - laravel-queue
      tags: [verify, services]

    - name: Display service status
      debug:
        msg: "{{ item.item }}: {{ 'Running' if item.status.ActiveState == 'active' else 'Not Running' }}"
      loop: "{{ service_status.results }}"
      tags: [verify, services]

    - name: Check if services are listening on expected ports
      wait_for:
        port: "{{ item.port }}"
        host: "{{ item.host | default('127.0.0.1') }}"
        timeout: 10
      register: port_check
      loop:
        - { port: "{{ nginx_http_port }}", name: "Nginx HTTP" }
        - { port: "{{ nginx_https_port }}", name: "Ngin<PERSON>TTPS" }
        - { port: 3306, name: "MariaDB" }
        - { port: "{{ redis_port }}", name: "Redis" }
      tags: [verify, ports]

    - name: Display port status
      debug:
        msg: "{{ item.item.name }} (port {{ item.item.port }}): {{ 'Listening' if not item.failed else 'Not Listening' }}"
      loop: "{{ port_check.results }}"
      tags: [verify, ports]

    - name: Test database connection
      mysql_db:
        name: "{{ mysql_database }}"
        state: present
        login_user: "{{ mysql_user }}"
        login_password: "{{ mysql_password }}"
      register: db_test
      tags: [verify, database]

    - name: Display database connection status
      debug:
        msg: "Database connection: {{ 'Success' if not db_test.failed else 'Failed' }}"
      tags: [verify, database]

    - name: Test Redis connection
      command: redis-cli -a "{{ redis_password }}" ping
      register: redis_test
      changed_when: false
      tags: [verify, redis]

    - name: Display Redis connection status
      debug:
        msg: "Redis connection: {{ 'Success' if redis_test.stdout == 'PONG' else 'Failed' }}"
      tags: [verify, redis]

    - name: Check PHP-FPM socket
      stat:
        path: "/run/php/php{{ php_version }}-fpm-worksuite.sock"
      register: php_socket
      tags: [verify, php]

    - name: Display PHP-FPM socket status
      debug:
        msg: "PHP-FPM socket: {{ 'Available' if php_socket.stat.exists else 'Not Available' }}"
      tags: [verify, php]

    - name: Test HTTP access
      uri:
        url: "http://{{ ansible_default_ipv4.address }}:{{ nginx_http_port }}"
        method: GET
        timeout: 30
        validate_certs: no
      register: http_test
      ignore_errors: yes
      tags: [verify, http]

    - name: Display HTTP access status
      debug:
        msg: "HTTP access (port {{ nginx_http_port }}): {{ 'Success (Status: ' + http_test.status|string + ')' if not http_test.failed else 'Failed' }}"
      tags: [verify, http]

    - name: Test HTTPS access
      uri:
        url: "https://{{ ansible_default_ipv4.address }}:{{ nginx_https_port }}"
        method: GET
        timeout: 30
        validate_certs: no
      register: https_test
      ignore_errors: yes
      tags: [verify, https]

    - name: Display HTTPS access status
      debug:
        msg: "HTTPS access (port {{ nginx_https_port }}): {{ 'Success (Status: ' + https_test.status|string + ')' if not https_test.failed else 'Failed' }}"
      tags: [verify, https]

    - name: Check application files
      stat:
        path: "{{ item }}"
      register: app_files
      loop:
        - "{{ app_root }}/artisan"
        - "{{ app_root }}/.env"
        - "{{ app_root }}/public/index.php"
        - "{{ app_storage }}"
        - "{{ app_bootstrap_cache }}"
      tags: [verify, files]

    - name: Display application files status
      debug:
        msg: "{{ item.item | basename }}: {{ 'Present' if item.stat.exists else 'Missing' }}"
      loop: "{{ app_files.results }}"
      tags: [verify, files]

    - name: Check application permissions
      stat:
        path: "{{ item }}"
      register: app_perms
      loop:
        - "{{ app_storage }}"
        - "{{ app_bootstrap_cache }}"
      tags: [verify, permissions]

    - name: Display application permissions
      debug:
        msg: "{{ item.item | basename }} permissions: {{ item.stat.mode if item.stat.exists else 'N/A' }}"
      loop: "{{ app_perms.results }}"
      tags: [verify, permissions]

    - name: Check log files
      find:
        paths: 
          - "{{ app_storage }}/logs"
          - "/var/log/nginx"
          - "/var/log/worksuite"
        patterns: "*.log"
      register: log_files
      tags: [verify, logs]

    - name: Display log files
      debug:
        msg: "Found {{ log_files.files | length }} log files"
      tags: [verify, logs]

    - name: Run Laravel artisan status check
      command: php artisan --version
      args:
        chdir: "{{ app_root }}"
      become_user: "{{ app_user }}"
      register: artisan_check
      changed_when: false
      tags: [verify, laravel]

    - name: Display Laravel status
      debug:
        msg: "Laravel: {{ artisan_check.stdout if not artisan_check.failed else 'Failed to run artisan' }}"
      tags: [verify, laravel]

    - name: Generate verification report
      template:
        src: templates/verification-report.j2
        dest: /tmp/worksuite-verification-report.txt
      tags: [verify, report]

    - name: Display verification summary
      debug:
        msg: |
          === WorkSuite SAAS Deployment Verification Summary ===
          
          Services Status:
          {% for result in service_status.results %}
          - {{ result.item }}: {{ 'Running' if result.status.ActiveState == 'active' else 'Not Running' }}
          {% endfor %}
          
          Network Status:
          {% for result in port_check.results %}
          - {{ result.item.name }} (port {{ result.item.port }}): {{ 'Listening' if not result.failed else 'Not Listening' }}
          {% endfor %}
          
          Application Status:
          - Database: {{ 'Connected' if not db_test.failed else 'Failed' }}
          - Redis: {{ 'Connected' if redis_test.stdout == 'PONG' else 'Failed' }}
          - HTTP Access: {{ 'Working' if not http_test.failed else 'Failed' }}
          - HTTPS Access: {{ 'Working' if not https_test.failed else 'Failed' }}
          - Laravel: {{ 'Working' if not artisan_check.failed else 'Failed' }}
          
          Access URLs:
          - HTTP: http://{{ ansible_default_ipv4.address }}:{{ nginx_http_port }}
          - HTTPS: https://{{ ansible_default_ipv4.address }}:{{ nginx_https_port }}
          
          Full report saved to: /tmp/worksuite-verification-report.txt
      tags: [verify, summary]
