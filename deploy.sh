#!/bin/bash
# WorkSuite SAAS Deployment Script
# This script runs the complete Ansible deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
print_status "Checking prerequisites..."

# Check if Ansible is installed
if ! command -v ansible-playbook &> /dev/null; then
    print_error "Ansible is not installed. Please install Ansible first."
    echo "Ubuntu/Debian: sudo apt update && sudo apt install ansible"
    echo "macOS: brew install ansible"
    exit 1
fi

# Check if SSH key exists
if [ ! -f "thaodoiti.pem" ]; then
    print_error "SSH key 'thaodoiti.pem' not found in current directory."
    exit 1
fi

# Set correct permissions on SSH key
chmod 400 thaodoiti.pem
print_status "Set SSH key permissions to 400"

# Check if source application exists
if [ ! -d "worksuite-saas-new-5.4.92/script" ]; then
    print_error "WorkSuite SAAS source directory 'worksuite-saas-new-5.4.92/script' not found."
    print_error "Please ensure the application source is present."
    exit 1
fi

# Test SSH connection
print_status "Testing SSH connection to EC2 instance..."
if ssh -i thaodoiti.pem -o ConnectTimeout=10 -o StrictHostKeyChecking=no <EMAIL> "echo 'SSH connection successful'" 2>/dev/null; then
    print_success "SSH connection test passed"
else
    print_error "Cannot connect to EC2 instance. Please check:"
    echo "  - Instance is running"
    echo "  - Security group allows SSH (port 22)"
    echo "  - SSH key is correct"
    echo "  - Network connectivity"
    exit 1
fi

# Function to run playbook with error handling
run_playbook() {
    local playbook=$1
    local description=$2
    
    print_status "Running $description..."
    
    if ansible-playbook -i inventory/hosts.yml "$playbook" -v; then
        print_success "$description completed successfully"
        return 0
    else
        print_error "$description failed"
        return 1
    fi
}

# Main deployment
print_status "Starting WorkSuite SAAS deployment..."
echo "========================================"

# Parse command line arguments
SKIP_INFRA=false
SKIP_SSL=false
SKIP_APP=false
SKIP_VERIFY=false
RUN_FULL=true

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-infrastructure)
            SKIP_INFRA=true
            RUN_FULL=false
            shift
            ;;
        --skip-ssl)
            SKIP_SSL=true
            RUN_FULL=false
            shift
            ;;
        --skip-application)
            SKIP_APP=true
            RUN_FULL=false
            shift
            ;;
        --skip-verify)
            SKIP_VERIFY=true
            RUN_FULL=false
            shift
            ;;
        --infrastructure-only)
            SKIP_SSL=true
            SKIP_APP=true
            SKIP_VERIFY=true
            RUN_FULL=false
            shift
            ;;
        --ssl-only)
            SKIP_INFRA=true
            SKIP_APP=true
            SKIP_VERIFY=true
            RUN_FULL=false
            shift
            ;;
        --application-only)
            SKIP_INFRA=true
            SKIP_SSL=true
            SKIP_VERIFY=true
            RUN_FULL=false
            shift
            ;;
        --verify-only)
            SKIP_INFRA=true
            SKIP_SSL=true
            SKIP_APP=true
            RUN_FULL=false
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --infrastructure-only    Run only infrastructure setup"
            echo "  --ssl-only              Run only SSL setup"
            echo "  --application-only      Run only application deployment"
            echo "  --verify-only           Run only verification"
            echo "  --skip-infrastructure   Skip infrastructure setup"
            echo "  --skip-ssl              Skip SSL setup"
            echo "  --skip-application      Skip application deployment"
            echo "  --skip-verify           Skip verification"
            echo "  --help                  Show this help message"
            echo ""
            echo "Default: Run complete deployment (all steps)"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run deployment steps
FAILED_STEPS=()

if [ "$RUN_FULL" = true ]; then
    # Run complete deployment
    if ! run_playbook "site.yml" "Complete Deployment"; then
        FAILED_STEPS+=("Complete Deployment")
    fi
else
    # Run individual steps
    if [ "$SKIP_INFRA" = false ]; then
        if ! run_playbook "infrastructure.yml" "Infrastructure Setup"; then
            FAILED_STEPS+=("Infrastructure Setup")
        fi
    fi

    if [ "$SKIP_SSL" = false ]; then
        if ! run_playbook "ssl-setup.yml" "SSL Certificate Setup"; then
            FAILED_STEPS+=("SSL Setup")
        fi
    fi

    if [ "$SKIP_APP" = false ]; then
        if ! run_playbook "application.yml" "Application Deployment"; then
            FAILED_STEPS+=("Application Deployment")
        fi
    fi

    if [ "$SKIP_VERIFY" = false ]; then
        if ! run_playbook "verify.yml" "Deployment Verification"; then
            FAILED_STEPS+=("Verification")
        fi
    fi
fi

# Summary
echo ""
echo "========================================"
if [ ${#FAILED_STEPS[@]} -eq 0 ]; then
    print_success "Deployment completed successfully!"
    echo ""
    print_status "Access your WorkSuite SAAS application at:"
    echo "  HTTP:  http://ec2-52-77-233-254.ap-southeast-1.compute.amazonaws.com:8601"
    echo "  HTTPS: https://ec2-52-77-233-254.ap-southeast-1.compute.amazonaws.com:8643"
    echo ""
    print_status "Next steps:"
    echo "  1. Access the application via the URLs above"
    echo "  2. Complete the WorkSuite SAAS setup wizard"
    echo "  3. Configure email settings"
    echo "  4. Set up monitoring and backups"
else
    print_error "Deployment completed with errors!"
    echo "Failed steps:"
    for step in "${FAILED_STEPS[@]}"; do
        echo "  - $step"
    done
    echo ""
    print_status "To troubleshoot:"
    echo "  1. Check the Ansible output above for specific errors"
    echo "  2. Run verification: ./deploy.sh --verify-only"
    echo "  3. Check service logs on the server"
    echo "  4. Re-run specific failed components"
    exit 1
fi
