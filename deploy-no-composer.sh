#!/bin/bash

# Deploy WorkSuite SAAS without using Composer
# This script copies the entire source code including vendor directory

set -e

echo "🚀 Starting WorkSuite SAAS Deployment (No Composer Mode)"
echo "=================================================="

# Check if source directory exists
SOURCE_DIR="./worksuite-saas-new-5.4.92"
if [ ! -d "$SOURCE_DIR" ]; then
    echo "❌ Error: Source directory $SOURCE_DIR not found!"
    echo "Please ensure the WorkSuite source code is extracted in the current directory."
    exit 1
fi

# Check if vendor directory exists in source
if [ ! -d "$SOURCE_DIR/vendor" ]; then
    echo "❌ Error: Vendor directory not found in $SOURCE_DIR"
    echo "Please ensure the source code includes the vendor directory with all dependencies."
    exit 1
fi

echo "✅ Source directory found: $SOURCE_DIR"
echo "✅ Vendor directory found: $SOURCE_DIR/vendor"

# Check Ansible inventory
if [ ! -f "inventory/hosts.yml" ]; then
    echo "❌ Error: Ansible inventory file not found!"
    echo "Please ensure inventory/hosts.yml exists."
    exit 1
fi

echo "✅ Ansible inventory found"

# Create tasks directory if not exists
mkdir -p tasks

echo "📦 Starting deployment..."

# Run the deployment playbook
ansible-playbook -i inventory/hosts.yml deploy-full-source.yml -v

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Deployment Successful!"
    echo "========================"
    echo ""
    echo "✅ Application deployed successfully"
    echo "✅ License verification disabled"
    echo "✅ Services started"
    echo ""
    echo "🌐 Access Information:"
    echo "   Public URL: https://erp.iti.id.vn"
    echo "   Login: <EMAIL>"
    echo "   Password: password123"
    echo ""
    echo "📝 Next Steps:"
    echo "   1. Visit https://erp.iti.id.vn"
    echo "   2. Login with the provided credentials"
    echo "   3. Configure your application settings"
    echo "   4. Create additional users as needed"
    echo ""
else
    echo ""
    echo "❌ Deployment Failed!"
    echo "===================="
    echo ""
    echo "Please check the error messages above and try again."
    echo "You can also check the backup files created during deployment."
    exit 1
fi
