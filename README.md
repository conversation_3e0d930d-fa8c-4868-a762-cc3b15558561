# WorkSuite SAAS Ansible Deployment

This repository contains Ansible playbooks for deploying WorkSuite SAAS application on bare metal Ubuntu servers.

## EC2 Instance Details

**Instance ID:** i-01d3fd9b04347d16d (thaodoiti)
**Public DNS:** ec2-52-77-233-254.ap-southeast-1.compute.amazonaws.com
**SSH Key:** thaodoiti.pem

### SSH Connection
```bash
# Ensure key permissions
chmod 400 "thaodoiti.pem"

# Connect to instance
ssh -i "thaodoiti.pem" <EMAIL>
```

## Deployment Overview

This Ansible deployment will:
- Install and configure Nginx, MariaDB, PHP-FPM 8.2, and Redis
- Deploy WorkSuite SAAS application from `worksuite-saas-new-5.4.92/`
- Configure services to run on HTTP port 8601 and HTTPS port 8643
- Set up SSL certificates for secure access
- Configure proper file permissions and optimization settings

## Quick Start

```bash
# Run the complete deployment
ansible-playbook -i inventory/hosts.yml site.yml

# Run specific components
ansible-playbook -i inventory/hosts.yml infrastructure.yml
ansible-playbook -i inventory/hosts.yml application.yml
```