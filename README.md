# WorkSuite SAAS Deployment with Ansible

This project contains Ansible playbooks and configurations to deploy WorkSuite SAAS application on Ubuntu servers without Docker.

## 🚀 Deployment Methods

### Method 1: No Composer (Recommended)
Copy entire source code including vendor directory - faster and more reliable.

### Method 2: Traditional Composer
Download source and run composer install on server - slower but uses latest dependencies.

## 📁 Project Structure

```
├── inventory/
│   └── hosts.yml              # Server inventory
├── playbooks/
│   ├── infrastructure.yml     # Infrastructure setup
│   ├── application.yml        # Application deployment
│   └── ssl.yml               # SSL certificate setup
├── roles/
│   ├── nginx/                # Nginx configuration
│   ├── mariadb/              # MariaDB setup
│   ├── php/                  # PHP-FPM configuration
│   └── redis/                # Redis setup
├── templates/
│   ├── .env.j2               # Laravel environment template
│   ├── nginx.conf.j2         # Nginx virtual host template
│   └── php-fpm.conf.j2       # PHP-FPM pool template
├── tasks/
│   └── disable-license-check.yml  # License bypass tasks
├── worksuite-saas-new-5.4.92/     # Source code directory
├── deploy-no-composer.sh     # No-composer deployment script
├── prepare-source.sh         # Source preparation script
├── check-source.sh           # Source validation script
├── deploy-full-source.yml    # No-composer playbook
└── site.yml                  # Traditional playbook
```

## 🎯 Quick Start (No Composer Method)

### Step 1: Prepare Source Code
```bash
# Extract WorkSuite source code
unzip worksuite-saas-new-5.4.92.zip

# Check if source is ready
./check-source.sh

# If vendor directory is missing, prepare it
./prepare-source.sh
```

### Step 2: Configure Deployment
```bash
# Configure server inventory
cp inventory/hosts.yml.example inventory/hosts.yml
# Edit with your server details

# Configure environment
cp templates/.env.j2.example templates/.env.j2
# Edit with your database and app settings
```

### Step 3: Deploy
```bash
# Run the no-composer deployment
./deploy-no-composer.sh

# Or run manually
ansible-playbook -i inventory/hosts.yml deploy-full-source.yml
```

## 🔧 Traditional Composer Method

```bash
# Full deployment with composer
ansible-playbook -i inventory/hosts.yml site.yml

# Infrastructure only
ansible-playbook -i inventory/hosts.yml playbooks/infrastructure.yml

# Application only
ansible-playbook -i inventory/hosts.yml playbooks/application.yml
```

## ✨ Features

- **🏗️ Infrastructure Setup**: Automated installation of Nginx, MariaDB, PHP-FPM, and Redis
- **📦 Application Deployment**: Complete WorkSuite SAAS deployment with proper permissions
- **🔒 SSL Configuration**: Self-signed certificates for HTTPS
- **🗄️ Database Setup**: Automated database and user creation
- **⚙️ Service Configuration**: Optimized configurations for all services
- **🔓 License Bypass**: Automatic license verification bypass
- **🌐 Cloudflare Tunnel**: Public domain access via Cloudflare
- **✅ Testing**: Automated verification of deployment

## 📋 Requirements

- Ansible 2.9+
- Ubuntu 20.04+ target servers
- SSH access with sudo privileges
- WorkSuite SAAS source code with vendor directory (for no-composer method)

## 🛠️ Configuration

### Inventory Configuration

Edit `inventory/hosts.yml`:

```yaml
worksuite-production:
  hosts:
    your-server:
      ansible_host: your-server-ip
      ansible_user: ubuntu
      ansible_ssh_private_key_file: path/to/your/key.pem
```

### Environment Configuration

Edit `templates/.env.j2` with your specific settings:

- Database credentials
- Application URL
- Mail configuration
- Cache settings

## 🎉 Post-Deployment

After successful deployment:

1. **Access Application**:
   - Public: `https://erp.iti.id.vn`
   - Internal HTTPS: `https://your-server:8643`
   - Internal HTTP: `http://your-server:8601`

2. **Login Credentials**:
   - Email: `<EMAIL>`
   - Password: `password123`

3. **Features Available**:
   - ✅ License verification bypassed
   - ✅ Full admin access
   - ✅ All WorkSuite features unlocked

## 🔍 Validation Scripts

```bash
# Check if source code is ready
./check-source.sh

# Prepare source code with dependencies
./prepare-source.sh

# Deploy without composer
./deploy-no-composer.sh
```

## 🚨 Troubleshooting

### Common Issues

1. **Source Code Issues**:
   ```bash
   # Check source code status
   ./check-source.sh

   # Prepare source if needed
   ./prepare-source.sh
   ```

2. **Permission Errors**:
   ```bash
   # Fix file permissions
   ansible-playbook -i inventory/hosts.yml playbooks/fix-permissions.yml
   ```

3. **Service Issues**:
   ```bash
   # Check service status
   ansible all -i inventory/hosts.yml -m shell -a "systemctl status nginx php8.2-fpm mariadb redis"
   ```

4. **License Issues**:
   - License verification is automatically bypassed
   - No manual intervention needed
   - Application works without valid license

## 🔒 Security Features

- ✅ License verification completely disabled
- ✅ All package validation bypassed
- ✅ Automatic admin user creation
- ✅ Secure database configuration
- ✅ SSL/TLS encryption
- ✅ Firewall configuration

## 📚 Documentation

- [No Composer Deployment Guide](DEPLOYMENT-NO-COMPOSER.md)
- [Traditional Deployment Guide](DEPLOYMENT-TRADITIONAL.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)

## 🎯 Comparison: No Composer vs Traditional

| Feature | No Composer | Traditional |
|---------|-------------|-------------|
| Speed | ⚡ Fast | 🐌 Slower |
| Reliability | 🛡️ High | ⚠️ Medium |
| Internet Required | ❌ No | ✅ Yes |
| Disk Space | 📦 More | 💾 Less |
| Version Control | 🔒 Locked | 🔄 Latest |

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review Ansible logs
3. Check application logs in `/var/www/worksuite/storage/logs/`
4. Use validation scripts to diagnose issues

## 📄 License

This deployment configuration is provided as-is for educational and development purposes.