---
# WorkSuite SAAS Complete Deployment Playbook
# This playbook orchestrates the complete deployment of WorkSuite SAAS
# on bare metal Ubuntu servers

- name: Pre-deployment checks
  hosts: worksuite_servers
  gather_facts: yes
  tasks:
    - name: Check if we can connect to the server
      ping:
      tags: [always]

    - name: Display server information
      debug:
        msg: |
          Deploying to: {{ inventory_hostname }}
          IP Address: {{ ansible_default_ipv4.address }}
          OS: {{ ansible_distribution }} {{ ansible_distribution_version }}
          Architecture: {{ ansible_architecture }}
      tags: [always]

    - name: Check available disk space
      shell: df -h / | awk 'NR==2 {print $4}'
      register: disk_space
      changed_when: false
      tags: [always]

    - name: Display disk space
      debug:
        msg: "Available disk space: {{ disk_space.stdout }}"
      tags: [always]

    - name: Check available memory
      debug:
        msg: "Available memory: {{ ansible_memtotal_mb }}MB"
      tags: [always]

# Step 1: Infrastructure Setup
- import_playbook: infrastructure.yml
  tags: [infrastructure]

# Step 2: SSL Certificate Setup
- import_playbook: ssl-setup.yml
  tags: [ssl]

# Step 3: Application Deployment
- import_playbook: application.yml
  tags: [application]

# Step 4: Verification
- import_playbook: verify.yml
  tags: [verify]

# Post-deployment summary
- name: Post-deployment summary
  hosts: worksuite_servers
  gather_facts: yes
  tasks:
    - name: Display deployment summary
      debug:
        msg: |
          ========================================
          WorkSuite SAAS Deployment Complete!
          ========================================
          
          Server: {{ inventory_hostname }}
          IP Address: {{ ansible_default_ipv4.address }}
          
          Access URLs:
          - HTTP:  http://{{ ansible_default_ipv4.address }}:{{ nginx_http_port }}
          - HTTPS: https://{{ ansible_default_ipv4.address }}:{{ nginx_https_port }}
          
          Services Installed:
          - Nginx (Web Server)
          - MariaDB (Database)
          - PHP {{ php_version }}-FPM (Application Server)
          - Redis (Cache & Sessions)
          - Laravel Queue Worker
          - Laravel Scheduler
          
          Important Files:
          - Application: {{ app_root }}
          - Environment: {{ app_root }}/.env
          - SSL Certificate: {{ ssl_cert_path }}
          - SSL Private Key: {{ ssl_key_path }}
          - Backup Script: /usr/local/bin/backup-worksuite.sh
          - SSL Info Script: /usr/local/bin/ssl-info.sh
          
          Database:
          - Name: {{ mysql_database }}
          - User: {{ mysql_user }}
          - Host: localhost:3306
          
          Redis:
          - Host: localhost:{{ redis_port }}
          - Password: [Configured]
          
          Next Steps:
          1. Access the application via the URLs above
          2. Complete WorkSuite SAAS setup if required
          3. Configure email settings in {{ app_root }}/.env
          4. Set up monitoring and alerting
          5. Schedule regular backups
          
          Logs Location:
          - Application: {{ app_storage }}/logs/
          - Nginx: /var/log/nginx/worksuite-*.log
          - PHP-FPM: /var/log/php{{ php_version }}-fpm-worksuite*.log
          
          Troubleshooting:
          - Check services: systemctl status <service-name>
          - View logs: journalctl -u <service-name>
          - Run verification: ansible-playbook -i inventory/hosts.yml verify.yml
          
          ========================================
      tags: [always]

    - name: Create deployment info file
      template:
        src: templates/deployment-info.j2
        dest: "{{ app_root }}/DEPLOYMENT_INFO.txt"
        owner: "{{ app_user }}"
        group: "{{ app_group }}"
        mode: '0644'
      tags: [always]

    - name: Final reminder
      debug:
        msg: |
          IMPORTANT SECURITY REMINDERS:
          
          1. Change default passwords in production:
             - Database root password
             - Database user password  
             - Redis password
          
          2. Configure firewall rules appropriately
          
          3. Set up SSL certificates from a trusted CA for production
          
          4. Configure email settings for notifications
          
          5. Set up monitoring and log rotation
          
          6. Test backup and restore procedures
          
          Deployment completed successfully!
      tags: [always]
