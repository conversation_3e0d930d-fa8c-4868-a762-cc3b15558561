---
- name: Deploy WorkSuite SAAS with Full Source Code (No Composer)
  hosts: worksuite-production
  become: yes
  vars:
    app_path: /var/www/worksuite
    source_path: ./worksuite-saas-new-5.4.92  # Local source code path
    web_user: worksuite
    web_group: worksuite

  tasks:
    - name: Stop services before deployment
      systemd:
        name: "{{ item }}"
        state: stopped
      loop:
        - nginx
        - php8.2-fpm
      ignore_errors: yes

    - name: Create backup of current deployment
      archive:
        path: "{{ app_path }}"
        dest: "/tmp/worksuite-backup-{{ ansible_date_time.epoch }}.tar.gz"
        format: gz
      when: ansible_stat.stat.exists
      vars:
        ansible_stat:
          stat:
            path: "{{ app_path }}"

    - name: Remove old application directory
      file:
        path: "{{ app_path }}"
        state: absent

    - name: Create application directory
      file:
        path: "{{ app_path }}"
        state: directory
        owner: "{{ web_user }}"
        group: "{{ web_group }}"
        mode: '0755'

    - name: Copy entire source code to server
      synchronize:
        src: "{{ source_path }}/"
        dest: "{{ app_path }}/"
        delete: yes
        recursive: yes
        rsync_opts:
          - "--exclude=.git"
          - "--exclude=.env"
          - "--exclude=storage/logs/*"
          - "--exclude=storage/framework/cache/*"
          - "--exclude=storage/framework/sessions/*"
          - "--exclude=storage/framework/views/*"
          - "--exclude=bootstrap/cache/*"
          - "--exclude=node_modules"
          - "--exclude=.DS_Store"
          - "--exclude=Thumbs.db"

    - name: Set ownership for application files
      file:
        path: "{{ app_path }}"
        owner: "{{ web_user }}"
        group: "{{ web_group }}"
        recurse: yes

    - name: Set proper permissions for directories
      file:
        path: "{{ item }}"
        mode: '0775'
        recurse: yes
      loop:
        - "{{ app_path }}/storage"
        - "{{ app_path }}/bootstrap/cache"
        - "{{ app_path }}/public"

    - name: Set proper permissions for files
      shell: |
        find {{ app_path }} -type f -exec chmod 644 {} \;
        find {{ app_path }} -type d -exec chmod 755 {} \;
        chmod -R 775 {{ app_path }}/storage
        chmod -R 775 {{ app_path }}/bootstrap/cache

    - name: Create .env file from template
      template:
        src: templates/.env.j2
        dest: "{{ app_path }}/.env"
        owner: "{{ web_user }}"
        group: "{{ web_group }}"
        mode: '0644'

    - name: Create storage directories if not exist
      file:
        path: "{{ app_path }}/storage/{{ item }}"
        state: directory
        owner: "{{ web_user }}"
        group: "{{ web_group }}"
        mode: '0775'
      loop:
        - logs
        - framework/cache
        - framework/sessions
        - framework/views
        - app/public

    - name: Clear Laravel caches
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan config:clear
        sudo -u {{ web_user }} php artisan cache:clear
        sudo -u {{ web_user }} php artisan route:clear
        sudo -u {{ web_user }} php artisan view:clear
      ignore_errors: yes

    - name: Run database migrations
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan migrate --force
      ignore_errors: yes

    - name: Generate application key if needed
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan key:generate --force
      when: not ansible_env_exists
      vars:
        ansible_env_exists: "{{ lookup('file', app_path + '/.env') | regex_search('APP_KEY=.+') }}"

    - name: Create symbolic link for storage
      file:
        src: "{{ app_path }}/storage/app/public"
        dest: "{{ app_path }}/public/storage"
        state: link
        owner: "{{ web_user }}"
        group: "{{ web_group }}"
      ignore_errors: yes

    - name: Apply license bypass modifications
      include_tasks: tasks/disable-license-check.yml

    - name: Start services
      systemd:
        name: "{{ item }}"
        state: started
        enabled: yes
      loop:
        - php8.2-fpm
        - nginx

    - name: Wait for services to be ready
      wait_for:
        port: "{{ item }}"
        host: localhost
        timeout: 30
      loop:
        - 80
        - 443

    - name: Test application
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 30
      register: app_test
      ignore_errors: yes

    - name: Display deployment results
      debug:
        msg: |
          🎉 Deployment Complete!
          
          ✅ Source code copied successfully
          ✅ Permissions set correctly
          ✅ Services restarted
          ✅ License checks disabled
          
          Application Status: {{ 'SUCCESS' if app_test.status == 200 or app_test.status == 302 else 'FAILED' }}
          
          Access URLs:
          - Internal: https://localhost:8643
          - Public: https://erp.iti.id.vn
          
          Login Credentials:
          - Email: <EMAIL>
          - Password: password123
