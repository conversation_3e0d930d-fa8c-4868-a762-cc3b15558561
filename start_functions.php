if (!function_exists('checkCompanyPackageIsValid')) {
    function checkCompanyPackageIsValid($companyId) {
        return true;
    }
}

if (!function_exists('checkCompanyCanAddMoreEmployees')) {
    function checkCompanyCanAddMoreEmployees($companyId) {
        return true;
    }
}

if (!function_exists('checkActiveCompany')) {
    function checkActiveCompany($companyId) {
        return cache()->rememberForever('user_' . $companyId . '_is_active', function () use ($companyId) {
            return Company::where('status', 'inactive')->where('id', $companyId)->exists();
        });
    }
}

if (!function_exists('clearCompanyValidPackageCache')) {
    function clearCompanyValidPackageCache($companyId) {
        if (is_null($companyId)) {
            return true;
        }
        cache()->forget('company_' . $companyId . '_valid_package');
        cache()->forget('company_' . $companyId . '_can_add_more_employees');
    }
}
