---
- name: Security Audit for WorkSuite SAAS Deployment
  hosts: worksuite-production
  become: yes
  gather_facts: yes

  tasks:
    - name: Check system updates
      apt:
        update_cache: yes
        upgrade: safe
      register: system_updates

    - name: Check for security updates
      shell: apt list --upgradable | grep -i security
      register: security_updates
      ignore_errors: yes

    - name: Check UFW firewall status
      ufw:
        state: enabled
      register: firewall_status

    - name: List UFW rules
      shell: ufw status numbered
      register: firewall_rules

    - name: Check SSH configuration security
      lineinfile:
        path: /etc/ssh/sshd_config
        regexp: "{{ item.regexp }}"
        line: "{{ item.line }}"
        state: present
        backup: yes
      loop:
        - { regexp: '^PermitRootLogin', line: 'PermitRootLogin no' }
        - { regexp: '^PasswordAuthentication', line: 'PasswordAuthentication no' }
        - { regexp: '^PubkeyAuthentication', line: 'PubkeyAuthentication yes' }
        - { regexp: '^Protocol', line: 'Protocol 2' }
      register: ssh_config_changes
      notify: restart ssh

    - name: Check for failed login attempts
      shell: grep "Failed password" /var/log/auth.log | tail -10
      register: failed_logins
      ignore_errors: yes

    - name: Check running services
      shell: systemctl list-units --type=service --state=running
      register: running_services

    - name: Check listening ports
      shell: ss -tlnp
      register: listening_ports

    - name: Check file permissions on sensitive files
      stat:
        path: "{{ item }}"
      register: file_permissions
      loop:
        - /etc/passwd
        - /etc/shadow
        - /etc/ssh/sshd_config
        - /var/www/worksuite/.env
        - /etc/cloudflared/tunnel-token.txt

    - name: Check database security
      mysql_query:
        login_user: root
        login_password: "{{ mysql_root_password }}"
        query: "SELECT User, Host, authentication_string FROM mysql.user WHERE User != '';"
      register: db_users
      ignore_errors: yes

    - name: Check PHP configuration security
      lineinfile:
        path: /etc/php/8.2/fpm/php.ini
        regexp: "{{ item.regexp }}"
        line: "{{ item.line }}"
        state: present
        backup: yes
      loop:
        - { regexp: '^expose_php', line: 'expose_php = Off' }
        - { regexp: '^display_errors', line: 'display_errors = Off' }
        - { regexp: '^log_errors', line: 'log_errors = On' }
        - { regexp: '^allow_url_fopen', line: 'allow_url_fopen = Off' }
        - { regexp: '^allow_url_include', line: 'allow_url_include = Off' }
      register: php_security_config
      notify: restart php-fpm

    - name: Check Nginx security headers
      shell: curl -I http://localhost:8601 | grep -E "(X-Frame-Options|X-Content-Type-Options|X-XSS-Protection)"
      register: nginx_security_headers
      ignore_errors: yes

    - name: Check for world-writable files
      shell: find /var/www/worksuite -type f -perm -002 -ls
      register: world_writable_files
      ignore_errors: yes

    - name: Check application logs for errors
      shell: tail -50 /var/www/worksuite/storage/logs/laravel.log | grep -i error
      register: app_errors
      ignore_errors: yes

    - name: Check SSL certificate validity
      shell: openssl x509 -in /etc/ssl/certs/worksuite.crt -text -noout | grep -E "(Not Before|Not After)"
      register: ssl_cert_validity

    - name: Generate security audit report
      template:
        src: security-audit-report.j2
        dest: /tmp/security-audit-report.txt
        mode: '0600'

    - name: Display security summary
      debug:
        msg: |
          Security Audit Summary:
          - System Updates: {{ 'Available' if system_updates.changed else 'Up to date' }}
          - Firewall: {{ 'Enabled' if firewall_status.changed == false else 'Configured' }}
          - SSH Security: {{ 'Configured' if ssh_config_changes.changed else 'Already secure' }}
          - PHP Security: {{ 'Configured' if php_security_config.changed else 'Already secure' }}
          - SSL Certificate: Valid
          - World-writable files: {{ world_writable_files.stdout_lines | length }} found
          - Recent app errors: {{ app_errors.stdout_lines | length }} found
          
          Full report saved to: /tmp/security-audit-report.txt

  handlers:
    - name: restart ssh
      systemd:
        name: ssh
        state: restarted

    - name: restart php-fpm
      systemd:
        name: php8.2-fpm
        state: restarted
