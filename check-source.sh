#!/bin/bash

# Script to check if WorkSuite SAAS source code is ready for deployment

echo "🔍 Checking WorkSuite SAAS Source Code"
echo "======================================"

SOURCE_DIR="worksuite-saas-new-5.4.92"
ERRORS=0

# Function to print status
print_status() {
    if [ $2 -eq 0 ]; then
        echo "  ✅ $1"
    else
        echo "  ❌ $1"
        ERRORS=$((ERRORS + 1))
    fi
}

# Check source directory
if [ -d "$SOURCE_DIR" ]; then
    print_status "Source directory exists" 0
else
    print_status "Source directory exists" 1
    echo "     Missing: $SOURCE_DIR"
fi

# Check composer.json
if [ -f "$SOURCE_DIR/composer.json" ]; then
    print_status "composer.json exists" 0
else
    print_status "composer.json exists" 1
fi

# Check vendor directory
if [ -d "$SOURCE_DIR/vendor" ]; then
    print_status "vendor directory exists" 0
    
    # Check vendor content
    VENDOR_COUNT=$(find "$SOURCE_DIR/vendor" -name "*.php" 2>/dev/null | wc -l)
    if [ "$VENDOR_COUNT" -gt 100 ]; then
        print_status "vendor directory populated ($VENDOR_COUNT PHP files)" 0
    else
        print_status "vendor directory populated" 1
        echo "     Only $VENDOR_COUNT PHP files found (expected > 100)"
    fi
else
    print_status "vendor directory exists" 1
fi

# Check important Laravel files
if [ -f "$SOURCE_DIR/artisan" ]; then
    print_status "artisan command exists" 0
else
    print_status "artisan command exists" 1
fi

if [ -d "$SOURCE_DIR/app" ]; then
    print_status "app directory exists" 0
else
    print_status "app directory exists" 1
fi

if [ -d "$SOURCE_DIR/public" ]; then
    print_status "public directory exists" 0
else
    print_status "public directory exists" 1
fi

# Check important packages
echo ""
echo "📦 Checking Important Packages:"

if [ -d "$SOURCE_DIR/vendor/laravel/framework" ]; then
    print_status "Laravel Framework" 0
else
    print_status "Laravel Framework" 1
fi

if [ -d "$SOURCE_DIR/vendor/froiden" ]; then
    print_status "Froiden Packages" 0
else
    print_status "Froiden Packages" 1
fi

if [ -f "$SOURCE_DIR/vendor/autoload.php" ]; then
    print_status "Composer Autoloader" 0
else
    print_status "Composer Autoloader" 1
fi

# Check deployment files
echo ""
echo "🚀 Checking Deployment Files:"

if [ -f "deploy-full-source.yml" ]; then
    print_status "Ansible playbook exists" 0
else
    print_status "Ansible playbook exists" 1
fi

if [ -f "inventory/hosts.yml" ]; then
    print_status "Ansible inventory exists" 0
else
    print_status "Ansible inventory exists" 1
fi

if [ -f "templates/.env.j2" ]; then
    print_status "Environment template exists" 0
else
    print_status "Environment template exists" 1
fi

if [ -d "tasks" ]; then
    print_status "Tasks directory exists" 0
else
    print_status "Tasks directory exists" 1
fi

# Summary
echo ""
echo "📊 Summary:"
echo "==========="

if [ $ERRORS -eq 0 ]; then
    echo "🎉 All checks passed! Source code is ready for deployment."
    echo ""
    echo "You can now run:"
    echo "  ./deploy-no-composer.sh"
    echo ""
    exit 0
else
    echo "❌ $ERRORS error(s) found. Please fix the issues above."
    echo ""
    echo "Common solutions:"
    echo "  1. If source directory is missing:"
    echo "     unzip worksuite-saas-new-5.4.92.zip"
    echo ""
    echo "  2. If vendor directory is missing or empty:"
    echo "     ./prepare-source.sh"
    echo ""
    echo "  3. If deployment files are missing:"
    echo "     Make sure you're in the correct directory with all deployment files"
    echo ""
    exit 1
fi
