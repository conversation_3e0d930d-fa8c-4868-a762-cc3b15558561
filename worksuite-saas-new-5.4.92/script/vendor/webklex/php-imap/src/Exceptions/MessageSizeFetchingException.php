<?php
/*
* File:     MessageSizeFetchingException.php
* Category: Exception
* Author:   <PERSON><PERSON>
* Created:  24.02.23 17:55
* Updated:  -
*
* Description: Exception thrown if fetching size for a message failed.
*  -
*/

namespace Webklex\PHPIMAP\Exceptions;

use \Exception;

/**
 * Class MessageSizeFetchingException
 *
 * @package Webklex\PHPIMAP\Exceptions
 */
class MessageSizeFetchingException extends Exception {

}
