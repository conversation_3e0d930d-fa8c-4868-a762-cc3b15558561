language: php

php:
  - hhvm
  - 7.0
  - 5.6
  - 5.5

matrix:
  fast_finish: true
  allow_failures:
    - php: hhvm

sudo: false

install: composer install --no-interaction

script:
  - mkdir -p build/logs
  - vendor/bin/phpunit --coverage-clover build/logs/clover.xml

after_success:
  - sh -c 'if [ "$TRAVIS_PHP_VERSION" != "hhvm" ]; then php vendor/bin/coveralls -v; fi;'

notifications:
  email:
    on_success: always
    on_failure: always