language: php

os: linux
dist: xenial

php:
  - 5.6
  - 7.0
  - 7.1
  - 7.2
  - 7.3
  - 7.4
  - hhvm

matrix:
  fast_finish: true
  allow_failures:
    - php: 7.0
    - php: 7.1
    - php: 7.3
    - php: 7.4
    - php: hhvm

sudo: false

before_script:
  - echo "memory_limit=2048M" >> ~/.phpenv/versions/$(phpenv version-name)/etc/conf.d/travis.ini
  - composer selfupdate
  - composer clearcache
  - composer install --no-interaction

install:
  - COMPOSER_MEMORY_LIMIT=-1 composer install --no-interaction

script:
  - phpunit

notifications:
  email:
    on_success: always
    on_failure: always

