<?php

namespace DummyNamespace;

use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Yajra\DataTables\Html\DataTableHtml;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Html\Editor\Editor;

class DummyClass extends DataTableHtml
{
    /**
     * Build the html builder.
     *
     * @throws \Exception
     */
    public function handle(): Builder
    {
        return $this->setTableId('DummyTableId')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    //->dom('DummyDOM')
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        DummyButtons
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::computed('action')
                  ->exportable(false)
                  ->printable(false)
                  ->width(60)
                  ->addClass('text-center'),
            DummyColumns
        ];
    }
}
