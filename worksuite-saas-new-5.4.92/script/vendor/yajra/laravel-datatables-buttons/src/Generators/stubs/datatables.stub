<?php

namespace DummyNamespace;

use DummyModel;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Ya<PERSON>ra\DataTables\Services\DataTable;
use Ya<PERSON>ra\DataTables\Html\Editor\Fields;
use Ya<PERSON>ra\DataTables\Html\Editor\Editor;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;

class DummyClass extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', 'DummyAction')
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(ModelName $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('DummyTableId')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    //->dom('DummyDOM')
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        DummyButtons
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::computed('action')
                  ->exportable(false)
                  ->printable(false)
                  ->width(60)
                  ->addClass('text-center'),
            DummyColumns
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'DummyFilename_' . date('YmdHis');
    }
}
