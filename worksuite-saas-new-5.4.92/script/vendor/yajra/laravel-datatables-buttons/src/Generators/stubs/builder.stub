<?php

namespace DummyNamespace;

use DummyModel;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Ya<PERSON>ra\DataTables\Services\DataTable;

class DummyClass extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', 'DummyAction')
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(ModelName $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return DummyBuilder::make();
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'DummyFilename_' . date('YmdHis');
    }
}
