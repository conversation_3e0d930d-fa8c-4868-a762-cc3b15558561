<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Taskrouter\V1\Workspace\Task;

use <PERSON>wi<PERSON>\Options;
use Twi<PERSON>\Values;

abstract class ReservationOptions {
    /**
     * @param string $reservationStatus Returns the list of reservations for a task
     *                                  with a specified ReservationStatus
     * @param string $workerSid The SID of the reserved Worker resource to read
     * @return ReadReservationOptions Options builder
     */
    public static function read(string $reservationStatus = Values::NONE, string $workerSid = Values::NONE): ReadReservationOptions {
        return new ReadReservationOptions($reservationStatus, $workerSid);
    }

    /**
     * @param string $reservationStatus The new status of the reservation
     * @param string $workerActivitySid The new worker activity SID if rejecting a
     *                                  reservation
     * @param string $instruction The assignment instruction for reservation
     * @param string $dequeuePostWorkActivitySid The SID of the Activity resource
     *                                           to start after executing a Dequeue
     *                                           instruction
     * @param string $dequeueFrom The Caller ID of the call to the worker when
     *                            executing a Dequeue instruction
     * @param string $dequeueRecord Whether to record both legs of a call when
     *                              executing a Dequeue instruction
     * @param int $dequeueTimeout Timeout for call when executing a Dequeue
     *                            instruction
     * @param string $dequeueTo The Contact URI of the worker when executing a
     *                          Dequeue instruction
     * @param string $dequeueStatusCallbackUrl The Callback URL for completed call
     *                                         event when executing a Dequeue
     *                                         instruction
     * @param string $callFrom The Caller ID of the outbound call when executing a
     *                         Call instruction
     * @param string $callRecord Whether to record both legs of a call when
     *                           executing a Call instruction
     * @param int $callTimeout Timeout for call when executing a Call instruction
     * @param string $callTo The Contact URI of the worker when executing a Call
     *                       instruction
     * @param string $callUrl TwiML URI executed on answering the worker's leg as a
     *                        result of the Call instruction
     * @param string $callStatusCallbackUrl The URL to call  for the completed call
     *                                      event when executing a Call instruction
     * @param bool $callAccept Whether to accept a reservation when executing a
     *                         Call instruction
     * @param string $redirectCallSid The Call SID of the call parked in the queue
     *                                when executing a Redirect instruction
     * @param bool $redirectAccept Whether the reservation should be accepted when
     *                             executing a Redirect instruction
     * @param string $redirectUrl TwiML URI to redirect the call to when executing
     *                            the Redirect instruction
     * @param string $to The Contact URI of the worker when executing a Conference
     *                   instruction
     * @param string $from The Caller ID of the call to the worker when executing a
     *                     Conference instruction
     * @param string $statusCallback The URL we should call to send status
     *                               information to your application
     * @param string $statusCallbackMethod The HTTP method we should use to call
     *                                     status_callback
     * @param string[] $statusCallbackEvent The call progress events that we will
     *                                      send to status_callback
     * @param int $timeout Timeout for call when executing a Conference instruction
     * @param bool $record Whether to record the participant and their conferences
     * @param bool $muted Whether to mute the agent
     * @param string $beep Whether to play a notification beep when the participant
     *                     joins
     * @param bool $startConferenceOnEnter Whether the conference starts when the
     *                                     participant joins the conference
     * @param bool $endConferenceOnExit Whether to end the conference when the
     *                                  agent leaves
     * @param string $waitUrl URL that hosts pre-conference hold music
     * @param string $waitMethod The HTTP method we should use to call `wait_url`
     * @param bool $earlyMedia Whether agents can hear the state of the outbound
     *                         call
     * @param int $maxParticipants The maximum number of agent conference
     *                             participants
     * @param string $conferenceStatusCallback The callback URL for conference
     *                                         events
     * @param string $conferenceStatusCallbackMethod HTTP method for requesting
     *                                               `conference_status_callback`
     *                                               URL
     * @param string[] $conferenceStatusCallbackEvent The conference status events
     *                                                that we will send to
     *                                                conference_status_callback
     * @param string $conferenceRecord Whether to record the conference the
     *                                 participant is joining
     * @param string $conferenceTrim How to trim leading and trailing silence from
     *                               your recorded conference audio files
     * @param string $recordingChannels Specify `mono` or `dual` recording channels
     * @param string $recordingStatusCallback The URL that we should call using the
     *                                        `recording_status_callback_method`
     *                                        when the recording status changes
     * @param string $recordingStatusCallbackMethod The HTTP method we should use
     *                                              when we call
     *                                              `recording_status_callback`
     * @param string $conferenceRecordingStatusCallback The URL we should call
     *                                                  using the
     *                                                  `conference_recording_status_callback_method` when the conference recording is available
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we
     *                                                        should use to call
     *                                                        `conference_recording_status_callback`
     * @param string $region The region where we should mix the conference audio
     * @param string $sipAuthUsername The SIP username used for authentication
     * @param string $sipAuthPassword The SIP password for authentication
     * @param string[] $dequeueStatusCallbackEvent The Call progress events sent
     *                                             via webhooks as a result of a
     *                                             Dequeue instruction
     * @param string $postWorkActivitySid The new worker activity SID after
     *                                    executing a Conference instruction
     * @param string $supervisorMode The Supervisor mode when executing the
     *                               Supervise instruction
     * @param string $supervisor The Supervisor SID/URI when executing the
     *                           Supervise instruction
     * @param bool $endConferenceOnCustomerExit Whether to end the conference when
     *                                          the customer leaves
     * @param bool $beepOnCustomerEntrance Whether to play a notification beep when
     *                                     the customer joins
     * @param string $ifMatch The If-Match HTTP request header
     * @return UpdateReservationOptions Options builder
     */
    public static function update(string $reservationStatus = Values::NONE, string $workerActivitySid = Values::NONE, string $instruction = Values::NONE, string $dequeuePostWorkActivitySid = Values::NONE, string $dequeueFrom = Values::NONE, string $dequeueRecord = Values::NONE, int $dequeueTimeout = Values::NONE, string $dequeueTo = Values::NONE, string $dequeueStatusCallbackUrl = Values::NONE, string $callFrom = Values::NONE, string $callRecord = Values::NONE, int $callTimeout = Values::NONE, string $callTo = Values::NONE, string $callUrl = Values::NONE, string $callStatusCallbackUrl = Values::NONE, bool $callAccept = Values::NONE, string $redirectCallSid = Values::NONE, bool $redirectAccept = Values::NONE, string $redirectUrl = Values::NONE, string $to = Values::NONE, string $from = Values::NONE, string $statusCallback = Values::NONE, string $statusCallbackMethod = Values::NONE, array $statusCallbackEvent = Values::ARRAY_NONE, int $timeout = Values::NONE, bool $record = Values::NONE, bool $muted = Values::NONE, string $beep = Values::NONE, bool $startConferenceOnEnter = Values::NONE, bool $endConferenceOnExit = Values::NONE, string $waitUrl = Values::NONE, string $waitMethod = Values::NONE, bool $earlyMedia = Values::NONE, int $maxParticipants = Values::NONE, string $conferenceStatusCallback = Values::NONE, string $conferenceStatusCallbackMethod = Values::NONE, array $conferenceStatusCallbackEvent = Values::ARRAY_NONE, string $conferenceRecord = Values::NONE, string $conferenceTrim = Values::NONE, string $recordingChannels = Values::NONE, string $recordingStatusCallback = Values::NONE, string $recordingStatusCallbackMethod = Values::NONE, string $conferenceRecordingStatusCallback = Values::NONE, string $conferenceRecordingStatusCallbackMethod = Values::NONE, string $region = Values::NONE, string $sipAuthUsername = Values::NONE, string $sipAuthPassword = Values::NONE, array $dequeueStatusCallbackEvent = Values::ARRAY_NONE, string $postWorkActivitySid = Values::NONE, string $supervisorMode = Values::NONE, string $supervisor = Values::NONE, bool $endConferenceOnCustomerExit = Values::NONE, bool $beepOnCustomerEntrance = Values::NONE, string $ifMatch = Values::NONE): UpdateReservationOptions {
        return new UpdateReservationOptions($reservationStatus, $workerActivitySid, $instruction, $dequeuePostWorkActivitySid, $dequeueFrom, $dequeueRecord, $dequeueTimeout, $dequeueTo, $dequeueStatusCallbackUrl, $callFrom, $callRecord, $callTimeout, $callTo, $callUrl, $callStatusCallbackUrl, $callAccept, $redirectCallSid, $redirectAccept, $redirectUrl, $to, $from, $statusCallback, $statusCallbackMethod, $statusCallbackEvent, $timeout, $record, $muted, $beep, $startConferenceOnEnter, $endConferenceOnExit, $waitUrl, $waitMethod, $earlyMedia, $maxParticipants, $conferenceStatusCallback, $conferenceStatusCallbackMethod, $conferenceStatusCallbackEvent, $conferenceRecord, $conferenceTrim, $recordingChannels, $recordingStatusCallback, $recordingStatusCallbackMethod, $conferenceRecordingStatusCallback, $conferenceRecordingStatusCallbackMethod, $region, $sipAuthUsername, $sipAuthPassword, $dequeueStatusCallbackEvent, $postWorkActivitySid, $supervisorMode, $supervisor, $endConferenceOnCustomerExit, $beepOnCustomerEntrance, $ifMatch);
    }
}

class ReadReservationOptions extends Options {
    /**
     * @param string $reservationStatus Returns the list of reservations for a task
     *                                  with a specified ReservationStatus
     * @param string $workerSid The SID of the reserved Worker resource to read
     */
    public function __construct(string $reservationStatus = Values::NONE, string $workerSid = Values::NONE) {
        $this->options['reservationStatus'] = $reservationStatus;
        $this->options['workerSid'] = $workerSid;
    }

    /**
     * Returns the list of reservations for a task with a specified ReservationStatus.  Can be: `pending`, `accepted`, `rejected`, or `timeout`.
     *
     * @param string $reservationStatus Returns the list of reservations for a task
     *                                  with a specified ReservationStatus
     * @return $this Fluent Builder
     */
    public function setReservationStatus(string $reservationStatus): self {
        $this->options['reservationStatus'] = $reservationStatus;
        return $this;
    }

    /**
     * The SID of the reserved Worker resource to read.
     *
     * @param string $workerSid The SID of the reserved Worker resource to read
     * @return $this Fluent Builder
     */
    public function setWorkerSid(string $workerSid): self {
        $this->options['workerSid'] = $workerSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.ReadReservationOptions ' . $options . ']';
    }
}

class UpdateReservationOptions extends Options {
    /**
     * @param string $reservationStatus The new status of the reservation
     * @param string $workerActivitySid The new worker activity SID if rejecting a
     *                                  reservation
     * @param string $instruction The assignment instruction for reservation
     * @param string $dequeuePostWorkActivitySid The SID of the Activity resource
     *                                           to start after executing a Dequeue
     *                                           instruction
     * @param string $dequeueFrom The Caller ID of the call to the worker when
     *                            executing a Dequeue instruction
     * @param string $dequeueRecord Whether to record both legs of a call when
     *                              executing a Dequeue instruction
     * @param int $dequeueTimeout Timeout for call when executing a Dequeue
     *                            instruction
     * @param string $dequeueTo The Contact URI of the worker when executing a
     *                          Dequeue instruction
     * @param string $dequeueStatusCallbackUrl The Callback URL for completed call
     *                                         event when executing a Dequeue
     *                                         instruction
     * @param string $callFrom The Caller ID of the outbound call when executing a
     *                         Call instruction
     * @param string $callRecord Whether to record both legs of a call when
     *                           executing a Call instruction
     * @param int $callTimeout Timeout for call when executing a Call instruction
     * @param string $callTo The Contact URI of the worker when executing a Call
     *                       instruction
     * @param string $callUrl TwiML URI executed on answering the worker's leg as a
     *                        result of the Call instruction
     * @param string $callStatusCallbackUrl The URL to call  for the completed call
     *                                      event when executing a Call instruction
     * @param bool $callAccept Whether to accept a reservation when executing a
     *                         Call instruction
     * @param string $redirectCallSid The Call SID of the call parked in the queue
     *                                when executing a Redirect instruction
     * @param bool $redirectAccept Whether the reservation should be accepted when
     *                             executing a Redirect instruction
     * @param string $redirectUrl TwiML URI to redirect the call to when executing
     *                            the Redirect instruction
     * @param string $to The Contact URI of the worker when executing a Conference
     *                   instruction
     * @param string $from The Caller ID of the call to the worker when executing a
     *                     Conference instruction
     * @param string $statusCallback The URL we should call to send status
     *                               information to your application
     * @param string $statusCallbackMethod The HTTP method we should use to call
     *                                     status_callback
     * @param string[] $statusCallbackEvent The call progress events that we will
     *                                      send to status_callback
     * @param int $timeout Timeout for call when executing a Conference instruction
     * @param bool $record Whether to record the participant and their conferences
     * @param bool $muted Whether to mute the agent
     * @param string $beep Whether to play a notification beep when the participant
     *                     joins
     * @param bool $startConferenceOnEnter Whether the conference starts when the
     *                                     participant joins the conference
     * @param bool $endConferenceOnExit Whether to end the conference when the
     *                                  agent leaves
     * @param string $waitUrl URL that hosts pre-conference hold music
     * @param string $waitMethod The HTTP method we should use to call `wait_url`
     * @param bool $earlyMedia Whether agents can hear the state of the outbound
     *                         call
     * @param int $maxParticipants The maximum number of agent conference
     *                             participants
     * @param string $conferenceStatusCallback The callback URL for conference
     *                                         events
     * @param string $conferenceStatusCallbackMethod HTTP method for requesting
     *                                               `conference_status_callback`
     *                                               URL
     * @param string[] $conferenceStatusCallbackEvent The conference status events
     *                                                that we will send to
     *                                                conference_status_callback
     * @param string $conferenceRecord Whether to record the conference the
     *                                 participant is joining
     * @param string $conferenceTrim How to trim leading and trailing silence from
     *                               your recorded conference audio files
     * @param string $recordingChannels Specify `mono` or `dual` recording channels
     * @param string $recordingStatusCallback The URL that we should call using the
     *                                        `recording_status_callback_method`
     *                                        when the recording status changes
     * @param string $recordingStatusCallbackMethod The HTTP method we should use
     *                                              when we call
     *                                              `recording_status_callback`
     * @param string $conferenceRecordingStatusCallback The URL we should call
     *                                                  using the
     *                                                  `conference_recording_status_callback_method` when the conference recording is available
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we
     *                                                        should use to call
     *                                                        `conference_recording_status_callback`
     * @param string $region The region where we should mix the conference audio
     * @param string $sipAuthUsername The SIP username used for authentication
     * @param string $sipAuthPassword The SIP password for authentication
     * @param string[] $dequeueStatusCallbackEvent The Call progress events sent
     *                                             via webhooks as a result of a
     *                                             Dequeue instruction
     * @param string $postWorkActivitySid The new worker activity SID after
     *                                    executing a Conference instruction
     * @param string $supervisorMode The Supervisor mode when executing the
     *                               Supervise instruction
     * @param string $supervisor The Supervisor SID/URI when executing the
     *                           Supervise instruction
     * @param bool $endConferenceOnCustomerExit Whether to end the conference when
     *                                          the customer leaves
     * @param bool $beepOnCustomerEntrance Whether to play a notification beep when
     *                                     the customer joins
     * @param string $ifMatch The If-Match HTTP request header
     */
    public function __construct(string $reservationStatus = Values::NONE, string $workerActivitySid = Values::NONE, string $instruction = Values::NONE, string $dequeuePostWorkActivitySid = Values::NONE, string $dequeueFrom = Values::NONE, string $dequeueRecord = Values::NONE, int $dequeueTimeout = Values::NONE, string $dequeueTo = Values::NONE, string $dequeueStatusCallbackUrl = Values::NONE, string $callFrom = Values::NONE, string $callRecord = Values::NONE, int $callTimeout = Values::NONE, string $callTo = Values::NONE, string $callUrl = Values::NONE, string $callStatusCallbackUrl = Values::NONE, bool $callAccept = Values::NONE, string $redirectCallSid = Values::NONE, bool $redirectAccept = Values::NONE, string $redirectUrl = Values::NONE, string $to = Values::NONE, string $from = Values::NONE, string $statusCallback = Values::NONE, string $statusCallbackMethod = Values::NONE, array $statusCallbackEvent = Values::ARRAY_NONE, int $timeout = Values::NONE, bool $record = Values::NONE, bool $muted = Values::NONE, string $beep = Values::NONE, bool $startConferenceOnEnter = Values::NONE, bool $endConferenceOnExit = Values::NONE, string $waitUrl = Values::NONE, string $waitMethod = Values::NONE, bool $earlyMedia = Values::NONE, int $maxParticipants = Values::NONE, string $conferenceStatusCallback = Values::NONE, string $conferenceStatusCallbackMethod = Values::NONE, array $conferenceStatusCallbackEvent = Values::ARRAY_NONE, string $conferenceRecord = Values::NONE, string $conferenceTrim = Values::NONE, string $recordingChannels = Values::NONE, string $recordingStatusCallback = Values::NONE, string $recordingStatusCallbackMethod = Values::NONE, string $conferenceRecordingStatusCallback = Values::NONE, string $conferenceRecordingStatusCallbackMethod = Values::NONE, string $region = Values::NONE, string $sipAuthUsername = Values::NONE, string $sipAuthPassword = Values::NONE, array $dequeueStatusCallbackEvent = Values::ARRAY_NONE, string $postWorkActivitySid = Values::NONE, string $supervisorMode = Values::NONE, string $supervisor = Values::NONE, bool $endConferenceOnCustomerExit = Values::NONE, bool $beepOnCustomerEntrance = Values::NONE, string $ifMatch = Values::NONE) {
        $this->options['reservationStatus'] = $reservationStatus;
        $this->options['workerActivitySid'] = $workerActivitySid;
        $this->options['instruction'] = $instruction;
        $this->options['dequeuePostWorkActivitySid'] = $dequeuePostWorkActivitySid;
        $this->options['dequeueFrom'] = $dequeueFrom;
        $this->options['dequeueRecord'] = $dequeueRecord;
        $this->options['dequeueTimeout'] = $dequeueTimeout;
        $this->options['dequeueTo'] = $dequeueTo;
        $this->options['dequeueStatusCallbackUrl'] = $dequeueStatusCallbackUrl;
        $this->options['callFrom'] = $callFrom;
        $this->options['callRecord'] = $callRecord;
        $this->options['callTimeout'] = $callTimeout;
        $this->options['callTo'] = $callTo;
        $this->options['callUrl'] = $callUrl;
        $this->options['callStatusCallbackUrl'] = $callStatusCallbackUrl;
        $this->options['callAccept'] = $callAccept;
        $this->options['redirectCallSid'] = $redirectCallSid;
        $this->options['redirectAccept'] = $redirectAccept;
        $this->options['redirectUrl'] = $redirectUrl;
        $this->options['to'] = $to;
        $this->options['from'] = $from;
        $this->options['statusCallback'] = $statusCallback;
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        $this->options['statusCallbackEvent'] = $statusCallbackEvent;
        $this->options['timeout'] = $timeout;
        $this->options['record'] = $record;
        $this->options['muted'] = $muted;
        $this->options['beep'] = $beep;
        $this->options['startConferenceOnEnter'] = $startConferenceOnEnter;
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        $this->options['waitUrl'] = $waitUrl;
        $this->options['waitMethod'] = $waitMethod;
        $this->options['earlyMedia'] = $earlyMedia;
        $this->options['maxParticipants'] = $maxParticipants;
        $this->options['conferenceStatusCallback'] = $conferenceStatusCallback;
        $this->options['conferenceStatusCallbackMethod'] = $conferenceStatusCallbackMethod;
        $this->options['conferenceStatusCallbackEvent'] = $conferenceStatusCallbackEvent;
        $this->options['conferenceRecord'] = $conferenceRecord;
        $this->options['conferenceTrim'] = $conferenceTrim;
        $this->options['recordingChannels'] = $recordingChannels;
        $this->options['recordingStatusCallback'] = $recordingStatusCallback;
        $this->options['recordingStatusCallbackMethod'] = $recordingStatusCallbackMethod;
        $this->options['conferenceRecordingStatusCallback'] = $conferenceRecordingStatusCallback;
        $this->options['conferenceRecordingStatusCallbackMethod'] = $conferenceRecordingStatusCallbackMethod;
        $this->options['region'] = $region;
        $this->options['sipAuthUsername'] = $sipAuthUsername;
        $this->options['sipAuthPassword'] = $sipAuthPassword;
        $this->options['dequeueStatusCallbackEvent'] = $dequeueStatusCallbackEvent;
        $this->options['postWorkActivitySid'] = $postWorkActivitySid;
        $this->options['supervisorMode'] = $supervisorMode;
        $this->options['supervisor'] = $supervisor;
        $this->options['endConferenceOnCustomerExit'] = $endConferenceOnCustomerExit;
        $this->options['beepOnCustomerEntrance'] = $beepOnCustomerEntrance;
        $this->options['ifMatch'] = $ifMatch;
    }

    /**
     * The new status of the reservation. Can be: `pending`, `accepted`, `rejected`, or `timeout`.
     *
     * @param string $reservationStatus The new status of the reservation
     * @return $this Fluent Builder
     */
    public function setReservationStatus(string $reservationStatus): self {
        $this->options['reservationStatus'] = $reservationStatus;
        return $this;
    }

    /**
     * The new worker activity SID if rejecting a reservation.
     *
     * @param string $workerActivitySid The new worker activity SID if rejecting a
     *                                  reservation
     * @return $this Fluent Builder
     */
    public function setWorkerActivitySid(string $workerActivitySid): self {
        $this->options['workerActivitySid'] = $workerActivitySid;
        return $this;
    }

    /**
     * The assignment instruction for reservation.
     *
     * @param string $instruction The assignment instruction for reservation
     * @return $this Fluent Builder
     */
    public function setInstruction(string $instruction): self {
        $this->options['instruction'] = $instruction;
        return $this;
    }

    /**
     * The SID of the Activity resource to start after executing a Dequeue instruction.
     *
     * @param string $dequeuePostWorkActivitySid The SID of the Activity resource
     *                                           to start after executing a Dequeue
     *                                           instruction
     * @return $this Fluent Builder
     */
    public function setDequeuePostWorkActivitySid(string $dequeuePostWorkActivitySid): self {
        $this->options['dequeuePostWorkActivitySid'] = $dequeuePostWorkActivitySid;
        return $this;
    }

    /**
     * The Caller ID of the call to the worker when executing a Dequeue instruction.
     *
     * @param string $dequeueFrom The Caller ID of the call to the worker when
     *                            executing a Dequeue instruction
     * @return $this Fluent Builder
     */
    public function setDequeueFrom(string $dequeueFrom): self {
        $this->options['dequeueFrom'] = $dequeueFrom;
        return $this;
    }

    /**
     * Whether to record both legs of a call when executing a Dequeue instruction or which leg to record.
     *
     * @param string $dequeueRecord Whether to record both legs of a call when
     *                              executing a Dequeue instruction
     * @return $this Fluent Builder
     */
    public function setDequeueRecord(string $dequeueRecord): self {
        $this->options['dequeueRecord'] = $dequeueRecord;
        return $this;
    }

    /**
     * Timeout for call when executing a Dequeue instruction.
     *
     * @param int $dequeueTimeout Timeout for call when executing a Dequeue
     *                            instruction
     * @return $this Fluent Builder
     */
    public function setDequeueTimeout(int $dequeueTimeout): self {
        $this->options['dequeueTimeout'] = $dequeueTimeout;
        return $this;
    }

    /**
     * The Contact URI of the worker when executing a Dequeue instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     *
     * @param string $dequeueTo The Contact URI of the worker when executing a
     *                          Dequeue instruction
     * @return $this Fluent Builder
     */
    public function setDequeueTo(string $dequeueTo): self {
        $this->options['dequeueTo'] = $dequeueTo;
        return $this;
    }

    /**
     * The Callback URL for completed call event when executing a Dequeue instruction.
     *
     * @param string $dequeueStatusCallbackUrl The Callback URL for completed call
     *                                         event when executing a Dequeue
     *                                         instruction
     * @return $this Fluent Builder
     */
    public function setDequeueStatusCallbackUrl(string $dequeueStatusCallbackUrl): self {
        $this->options['dequeueStatusCallbackUrl'] = $dequeueStatusCallbackUrl;
        return $this;
    }

    /**
     * The Caller ID of the outbound call when executing a Call instruction.
     *
     * @param string $callFrom The Caller ID of the outbound call when executing a
     *                         Call instruction
     * @return $this Fluent Builder
     */
    public function setCallFrom(string $callFrom): self {
        $this->options['callFrom'] = $callFrom;
        return $this;
    }

    /**
     * Whether to record both legs of a call when executing a Call instruction or which leg to record.
     *
     * @param string $callRecord Whether to record both legs of a call when
     *                           executing a Call instruction
     * @return $this Fluent Builder
     */
    public function setCallRecord(string $callRecord): self {
        $this->options['callRecord'] = $callRecord;
        return $this;
    }

    /**
     * Timeout for call when executing a Call instruction.
     *
     * @param int $callTimeout Timeout for call when executing a Call instruction
     * @return $this Fluent Builder
     */
    public function setCallTimeout(int $callTimeout): self {
        $this->options['callTimeout'] = $callTimeout;
        return $this;
    }

    /**
     * The Contact URI of the worker when executing a Call instruction.  Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     *
     * @param string $callTo The Contact URI of the worker when executing a Call
     *                       instruction
     * @return $this Fluent Builder
     */
    public function setCallTo(string $callTo): self {
        $this->options['callTo'] = $callTo;
        return $this;
    }

    /**
     * TwiML URI executed on answering the worker's leg as a result of the Call instruction.
     *
     * @param string $callUrl TwiML URI executed on answering the worker's leg as a
     *                        result of the Call instruction
     * @return $this Fluent Builder
     */
    public function setCallUrl(string $callUrl): self {
        $this->options['callUrl'] = $callUrl;
        return $this;
    }

    /**
     * The URL to call  for the completed call event when executing a Call instruction.
     *
     * @param string $callStatusCallbackUrl The URL to call  for the completed call
     *                                      event when executing a Call instruction
     * @return $this Fluent Builder
     */
    public function setCallStatusCallbackUrl(string $callStatusCallbackUrl): self {
        $this->options['callStatusCallbackUrl'] = $callStatusCallbackUrl;
        return $this;
    }

    /**
     * Whether to accept a reservation when executing a Call instruction.
     *
     * @param bool $callAccept Whether to accept a reservation when executing a
     *                         Call instruction
     * @return $this Fluent Builder
     */
    public function setCallAccept(bool $callAccept): self {
        $this->options['callAccept'] = $callAccept;
        return $this;
    }

    /**
     * The Call SID of the call parked in the queue when executing a Redirect instruction.
     *
     * @param string $redirectCallSid The Call SID of the call parked in the queue
     *                                when executing a Redirect instruction
     * @return $this Fluent Builder
     */
    public function setRedirectCallSid(string $redirectCallSid): self {
        $this->options['redirectCallSid'] = $redirectCallSid;
        return $this;
    }

    /**
     * Whether the reservation should be accepted when executing a Redirect instruction.
     *
     * @param bool $redirectAccept Whether the reservation should be accepted when
     *                             executing a Redirect instruction
     * @return $this Fluent Builder
     */
    public function setRedirectAccept(bool $redirectAccept): self {
        $this->options['redirectAccept'] = $redirectAccept;
        return $this;
    }

    /**
     * TwiML URI to redirect the call to when executing the Redirect instruction.
     *
     * @param string $redirectUrl TwiML URI to redirect the call to when executing
     *                            the Redirect instruction
     * @return $this Fluent Builder
     */
    public function setRedirectUrl(string $redirectUrl): self {
        $this->options['redirectUrl'] = $redirectUrl;
        return $this;
    }

    /**
     * The Contact URI of the worker when executing a Conference instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     *
     * @param string $to The Contact URI of the worker when executing a Conference
     *                   instruction
     * @return $this Fluent Builder
     */
    public function setTo(string $to): self {
        $this->options['to'] = $to;
        return $this;
    }

    /**
     * The Caller ID of the call to the worker when executing a Conference instruction.
     *
     * @param string $from The Caller ID of the call to the worker when executing a
     *                     Conference instruction
     * @return $this Fluent Builder
     */
    public function setFrom(string $from): self {
        $this->options['from'] = $from;
        return $this;
    }

    /**
     * The URL we should call using the `status_callback_method` to send status information to your application.
     *
     * @param string $statusCallback The URL we should call to send status
     *                               information to your application
     * @return $this Fluent Builder
     */
    public function setStatusCallback(string $statusCallback): self {
        $this->options['statusCallback'] = $statusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `status_callback`. Can be: `POST` or `GET` and the default is `POST`.
     *
     * @param string $statusCallbackMethod The HTTP method we should use to call
     *                                     status_callback
     * @return $this Fluent Builder
     */
    public function setStatusCallbackMethod(string $statusCallbackMethod): self {
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        return $this;
    }

    /**
     * The call progress events that we will send to `status_callback`. Can be: `initiated`, `ringing`, `answered`, or `completed`.
     *
     * @param string[] $statusCallbackEvent The call progress events that we will
     *                                      send to status_callback
     * @return $this Fluent Builder
     */
    public function setStatusCallbackEvent(array $statusCallbackEvent): self {
        $this->options['statusCallbackEvent'] = $statusCallbackEvent;
        return $this;
    }

    /**
     * Timeout for call when executing a Conference instruction.
     *
     * @param int $timeout Timeout for call when executing a Conference instruction
     * @return $this Fluent Builder
     */
    public function setTimeout(int $timeout): self {
        $this->options['timeout'] = $timeout;
        return $this;
    }

    /**
     * Whether to record the participant and their conferences, including the time between conferences. The default is `false`.
     *
     * @param bool $record Whether to record the participant and their conferences
     * @return $this Fluent Builder
     */
    public function setRecord(bool $record): self {
        $this->options['record'] = $record;
        return $this;
    }

    /**
     * Whether the agent is muted in the conference. The default is `false`.
     *
     * @param bool $muted Whether to mute the agent
     * @return $this Fluent Builder
     */
    public function setMuted(bool $muted): self {
        $this->options['muted'] = $muted;
        return $this;
    }

    /**
     * Whether to play a notification beep when the participant joins or when to play a beep. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     *
     * @param string $beep Whether to play a notification beep when the participant
     *                     joins
     * @return $this Fluent Builder
     */
    public function setBeep(string $beep): self {
        $this->options['beep'] = $beep;
        return $this;
    }

    /**
     * Whether to start the conference when the participant joins, if it has not already started. The default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     *
     * @param bool $startConferenceOnEnter Whether the conference starts when the
     *                                     participant joins the conference
     * @return $this Fluent Builder
     */
    public function setStartConferenceOnEnter(bool $startConferenceOnEnter): self {
        $this->options['startConferenceOnEnter'] = $startConferenceOnEnter;
        return $this;
    }

    /**
     * Whether to end the conference when the agent leaves.
     *
     * @param bool $endConferenceOnExit Whether to end the conference when the
     *                                  agent leaves
     * @return $this Fluent Builder
     */
    public function setEndConferenceOnExit(bool $endConferenceOnExit): self {
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        return $this;
    }

    /**
     * The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
     *
     * @param string $waitUrl URL that hosts pre-conference hold music
     * @return $this Fluent Builder
     */
    public function setWaitUrl(string $waitUrl): self {
        $this->options['waitUrl'] = $waitUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     *
     * @param string $waitMethod The HTTP method we should use to call `wait_url`
     * @return $this Fluent Builder
     */
    public function setWaitMethod(string $waitMethod): self {
        $this->options['waitMethod'] = $waitMethod;
        return $this;
    }

    /**
     * Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. The default is `true`.
     *
     * @param bool $earlyMedia Whether agents can hear the state of the outbound
     *                         call
     * @return $this Fluent Builder
     */
    public function setEarlyMedia(bool $earlyMedia): self {
        $this->options['earlyMedia'] = $earlyMedia;
        return $this;
    }

    /**
     * The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     *
     * @param int $maxParticipants The maximum number of agent conference
     *                             participants
     * @return $this Fluent Builder
     */
    public function setMaxParticipants(int $maxParticipants): self {
        $this->options['maxParticipants'] = $maxParticipants;
        return $this;
    }

    /**
     * The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     *
     * @param string $conferenceStatusCallback The callback URL for conference
     *                                         events
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallback(string $conferenceStatusCallback): self {
        $this->options['conferenceStatusCallback'] = $conferenceStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $conferenceStatusCallbackMethod HTTP method for requesting
     *                                               `conference_status_callback`
     *                                               URL
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallbackMethod(string $conferenceStatusCallbackMethod): self {
        $this->options['conferenceStatusCallbackMethod'] = $conferenceStatusCallbackMethod;
        return $this;
    }

    /**
     * The conference status events that we will send to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `speaker`.
     *
     * @param string[] $conferenceStatusCallbackEvent The conference status events
     *                                                that we will send to
     *                                                conference_status_callback
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallbackEvent(array $conferenceStatusCallbackEvent): self {
        $this->options['conferenceStatusCallbackEvent'] = $conferenceStatusCallbackEvent;
        return $this;
    }

    /**
     * Whether to record the conference the participant is joining or when to record the conference. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     *
     * @param string $conferenceRecord Whether to record the conference the
     *                                 participant is joining
     * @return $this Fluent Builder
     */
    public function setConferenceRecord(string $conferenceRecord): self {
        $this->options['conferenceRecord'] = $conferenceRecord;
        return $this;
    }

    /**
     * How to trim the leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     *
     * @param string $conferenceTrim How to trim leading and trailing silence from
     *                               your recorded conference audio files
     * @return $this Fluent Builder
     */
    public function setConferenceTrim(string $conferenceTrim): self {
        $this->options['conferenceTrim'] = $conferenceTrim;
        return $this;
    }

    /**
     * The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     *
     * @param string $recordingChannels Specify `mono` or `dual` recording channels
     * @return $this Fluent Builder
     */
    public function setRecordingChannels(string $recordingChannels): self {
        $this->options['recordingChannels'] = $recordingChannels;
        return $this;
    }

    /**
     * The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     *
     * @param string $recordingStatusCallback The URL that we should call using the
     *                                        `recording_status_callback_method`
     *                                        when the recording status changes
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallback(string $recordingStatusCallback): self {
        $this->options['recordingStatusCallback'] = $recordingStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $recordingStatusCallbackMethod The HTTP method we should use
     *                                              when we call
     *                                              `recording_status_callback`
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallbackMethod(string $recordingStatusCallbackMethod): self {
        $this->options['recordingStatusCallbackMethod'] = $recordingStatusCallbackMethod;
        return $this;
    }

    /**
     * The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     *
     * @param string $conferenceRecordingStatusCallback The URL we should call
     *                                                  using the
     *                                                  `conference_recording_status_callback_method` when the conference recording is available
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallback(string $conferenceRecordingStatusCallback): self {
        $this->options['conferenceRecordingStatusCallback'] = $conferenceRecordingStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we
     *                                                        should use to call
     *                                                        `conference_recording_status_callback`
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallbackMethod(string $conferenceRecordingStatusCallbackMethod): self {
        $this->options['conferenceRecordingStatusCallbackMethod'] = $conferenceRecordingStatusCallbackMethod;
        return $this;
    }

    /**
     * The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     *
     * @param string $region The region where we should mix the conference audio
     * @return $this Fluent Builder
     */
    public function setRegion(string $region): self {
        $this->options['region'] = $region;
        return $this;
    }

    /**
     * The SIP username used for authentication.
     *
     * @param string $sipAuthUsername The SIP username used for authentication
     * @return $this Fluent Builder
     */
    public function setSipAuthUsername(string $sipAuthUsername): self {
        $this->options['sipAuthUsername'] = $sipAuthUsername;
        return $this;
    }

    /**
     * The SIP password for authentication.
     *
     * @param string $sipAuthPassword The SIP password for authentication
     * @return $this Fluent Builder
     */
    public function setSipAuthPassword(string $sipAuthPassword): self {
        $this->options['sipAuthPassword'] = $sipAuthPassword;
        return $this;
    }

    /**
     * The Call progress events sent via webhooks as a result of a Dequeue instruction.
     *
     * @param string[] $dequeueStatusCallbackEvent The Call progress events sent
     *                                             via webhooks as a result of a
     *                                             Dequeue instruction
     * @return $this Fluent Builder
     */
    public function setDequeueStatusCallbackEvent(array $dequeueStatusCallbackEvent): self {
        $this->options['dequeueStatusCallbackEvent'] = $dequeueStatusCallbackEvent;
        return $this;
    }

    /**
     * The new worker activity SID after executing a Conference instruction.
     *
     * @param string $postWorkActivitySid The new worker activity SID after
     *                                    executing a Conference instruction
     * @return $this Fluent Builder
     */
    public function setPostWorkActivitySid(string $postWorkActivitySid): self {
        $this->options['postWorkActivitySid'] = $postWorkActivitySid;
        return $this;
    }

    /**
     * The Supervisor mode when executing the Supervise instruction.
     *
     * @param string $supervisorMode The Supervisor mode when executing the
     *                               Supervise instruction
     * @return $this Fluent Builder
     */
    public function setSupervisorMode(string $supervisorMode): self {
        $this->options['supervisorMode'] = $supervisorMode;
        return $this;
    }

    /**
     * The Supervisor SID/URI when executing the Supervise instruction.
     *
     * @param string $supervisor The Supervisor SID/URI when executing the
     *                           Supervise instruction
     * @return $this Fluent Builder
     */
    public function setSupervisor(string $supervisor): self {
        $this->options['supervisor'] = $supervisor;
        return $this;
    }

    /**
     * Whether to end the conference when the customer leaves.
     *
     * @param bool $endConferenceOnCustomerExit Whether to end the conference when
     *                                          the customer leaves
     * @return $this Fluent Builder
     */
    public function setEndConferenceOnCustomerExit(bool $endConferenceOnCustomerExit): self {
        $this->options['endConferenceOnCustomerExit'] = $endConferenceOnCustomerExit;
        return $this;
    }

    /**
     * Whether to play a notification beep when the customer joins.
     *
     * @param bool $beepOnCustomerEntrance Whether to play a notification beep when
     *                                     the customer joins
     * @return $this Fluent Builder
     */
    public function setBeepOnCustomerEntrance(bool $beepOnCustomerEntrance): self {
        $this->options['beepOnCustomerEntrance'] = $beepOnCustomerEntrance;
        return $this;
    }

    /**
     * The If-Match HTTP request header
     *
     * @param string $ifMatch The If-Match HTTP request header
     * @return $this Fluent Builder
     */
    public function setIfMatch(string $ifMatch): self {
        $this->options['ifMatch'] = $ifMatch;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.UpdateReservationOptions ' . $options . ']';
    }
}