<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Taskrouter\V1\Workspace\Worker;

use <PERSON><PERSON><PERSON>\Options;
use <PERSON>wi<PERSON>\Values;

abstract class WorkersRealTimeStatisticsOptions {
    /**
     * @param string $taskChannel Only calculate real-time statistics on this
     *                            TaskChannel
     * @return FetchWorkersRealTimeStatisticsOptions Options builder
     */
    public static function fetch(string $taskChannel = Values::NONE): FetchWorkersRealTimeStatisticsOptions {
        return new FetchWorkersRealTimeStatisticsOptions($taskChannel);
    }
}

class FetchWorkersRealTimeStatisticsOptions extends Options {
    /**
     * @param string $taskChannel Only calculate real-time statistics on this
     *                            TaskChannel
     */
    public function __construct(string $taskChannel = Values::NONE) {
        $this->options['taskChannel'] = $taskChannel;
    }

    /**
     * Only calculate real-time statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.
     *
     * @param string $taskChannel Only calculate real-time statistics on this
     *                            TaskChannel
     * @return $this Fluent Builder
     */
    public function setTaskChannel(string $taskChannel): self {
        $this->options['taskChannel'] = $taskChannel;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.FetchWorkersRealTimeStatisticsOptions ' . $options . ']';
    }
}