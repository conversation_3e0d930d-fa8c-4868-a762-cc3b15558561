<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Sync\V1\Service\SyncStream;

use <PERSON><PERSON>lio\Exceptions\TwilioException;
use <PERSON><PERSON><PERSON>\ListResource;
use <PERSON><PERSON><PERSON>\Serialize;
use Twilio\Values;
use Twilio\Version;

class StreamMessageList extends ListResource {
    /**
     * Construct the StreamMessageList
     *
     * @param Version $version Version that contains the resource
     * @param string $serviceSid The SID of the Sync Service that the resource is
     *                           associated with
     * @param string $streamSid The unique string that identifies the resource
     */
    public function __construct(Version $version, string $serviceSid, string $streamSid) {
        parent::__construct($version);

        // Path Solution
        $this->solution = ['serviceSid' => $serviceSid, 'streamSid' => $streamSid, ];

        $this->uri = '/Services/' . \rawurlencode($serviceSid) . '/Streams/' . \rawurlencode($streamSid) . '/Messages';
    }

    /**
     * Create the StreamMessageInstance
     *
     * @param array $data A JSON string that represents an arbitrary, schema-less
     *                    object that makes up the Stream Message body
     * @return StreamMessageInstance Created StreamMessageInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(array $data): StreamMessageInstance {
        $data = Values::of(['Data' => Serialize::jsonObject($data), ]);

        $payload = $this->version->create('POST', $this->uri, [], $data);

        return new StreamMessageInstance(
            $this->version,
            $payload,
            $this->solution['serviceSid'],
            $this->solution['streamSid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Sync.V1.StreamMessageList]';
    }
}