<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Taskrouter\V1\Workspace\TaskQueue;

use <PERSON><PERSON><PERSON>\Options;
use Twilio\Values;

abstract class TaskQueueCumulativeStatisticsOptions {
    /**
     * @param \DateTime $endDate Only calculate statistics from on or before this
     *                           date
     * @param int $minutes Only calculate statistics since this many minutes in the
     *                     past
     * @param \DateTime $startDate Only calculate statistics from on or after this
     *                             date
     * @param string $taskChannel Only calculate cumulative statistics on this
     *                            TaskChannel
     * @param string $splitByWaitTime A comma separated list of values that
     *                                describes the thresholds, in seconds, to
     *                                calculate statistics on
     * @return FetchTaskQueueCumulativeStatisticsOptions Options builder
     */
    public static function fetch(\DateTime $endDate = Values::NONE, int $minutes = Values::NONE, \DateTime $startDate = Values::NONE, string $taskChannel = Values::NONE, string $splitByWaitTime = Values::NONE): FetchTaskQueueCumulativeStatisticsOptions {
        return new FetchTaskQueueCumulativeStatisticsOptions($endDate, $minutes, $startDate, $taskChannel, $splitByWaitTime);
    }
}

class FetchTaskQueueCumulativeStatisticsOptions extends Options {
    /**
     * @param \DateTime $endDate Only calculate statistics from on or before this
     *                           date
     * @param int $minutes Only calculate statistics since this many minutes in the
     *                     past
     * @param \DateTime $startDate Only calculate statistics from on or after this
     *                             date
     * @param string $taskChannel Only calculate cumulative statistics on this
     *                            TaskChannel
     * @param string $splitByWaitTime A comma separated list of values that
     *                                describes the thresholds, in seconds, to
     *                                calculate statistics on
     */
    public function __construct(\DateTime $endDate = Values::NONE, int $minutes = Values::NONE, \DateTime $startDate = Values::NONE, string $taskChannel = Values::NONE, string $splitByWaitTime = Values::NONE) {
        $this->options['endDate'] = $endDate;
        $this->options['minutes'] = $minutes;
        $this->options['startDate'] = $startDate;
        $this->options['taskChannel'] = $taskChannel;
        $this->options['splitByWaitTime'] = $splitByWaitTime;
    }

    /**
     * Only calculate statistics from this date and time and earlier, specified in GMT as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time.
     *
     * @param \DateTime $endDate Only calculate statistics from on or before this
     *                           date
     * @return $this Fluent Builder
     */
    public function setEndDate(\DateTime $endDate): self {
        $this->options['endDate'] = $endDate;
        return $this;
    }

    /**
     * Only calculate statistics since this many minutes in the past. The default is 15 minutes.
     *
     * @param int $minutes Only calculate statistics since this many minutes in the
     *                     past
     * @return $this Fluent Builder
     */
    public function setMinutes(int $minutes): self {
        $this->options['minutes'] = $minutes;
        return $this;
    }

    /**
     * Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
     *
     * @param \DateTime $startDate Only calculate statistics from on or after this
     *                             date
     * @return $this Fluent Builder
     */
    public function setStartDate(\DateTime $startDate): self {
        $this->options['startDate'] = $startDate;
        return $this;
    }

    /**
     * Only calculate cumulative statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.
     *
     * @param string $taskChannel Only calculate cumulative statistics on this
     *                            TaskChannel
     * @return $this Fluent Builder
     */
    public function setTaskChannel(string $taskChannel): self {
        $this->options['taskChannel'] = $taskChannel;
        return $this;
    }

    /**
     * A comma separated list of values that describes the thresholds, in seconds, to calculate statistics on. For each threshold specified, the number of Tasks canceled and reservations accepted above and below the specified thresholds in seconds are computed. TaskRouter will calculate statistics on up to 10,000 Tasks/Reservations for any given threshold.
     *
     * @param string $splitByWaitTime A comma separated list of values that
     *                                describes the thresholds, in seconds, to
     *                                calculate statistics on
     * @return $this Fluent Builder
     */
    public function setSplitByWaitTime(string $splitByWaitTime): self {
        $this->options['splitByWaitTime'] = $splitByWaitTime;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.FetchTaskQueueCumulativeStatisticsOptions ' . $options . ']';
    }
}