<?php

declare(strict_types=1);

namespace Vee<PERSON>ee\Xml\Dom\Mapper;

use Closure;
use DOMDocument;
use Vee<PERSON>ee\Xml\Dom\Document;
use Vee<PERSON>ee\Xml\Xslt\Processor;
use XSLTProcessor;

/**
 * @param list<callable(XSLTProcessor): XSLTProcessor> $configurators
 * @return Closure(DOMDocument): string
 */
function xslt_template(Document $template, callable ... $configurators): Closure
{
    return static fn (DOMDocument $document): string
        => Processor::fromTemplateDocument($template, ...$configurators)->transformDocumentToString(
            Document::fromUnsafeDocument($document)
        );
}
