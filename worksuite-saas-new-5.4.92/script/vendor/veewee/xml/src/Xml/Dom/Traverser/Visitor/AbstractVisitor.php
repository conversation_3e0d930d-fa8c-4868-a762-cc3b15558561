<?php

declare(strict_types=1);

namespace Vee<PERSON>ee\Xml\Dom\Traverser\Visitor;

use DOMNode;
use Vee<PERSON>ee\Xml\Dom\Traverser\Action;
use Vee<PERSON>ee\Xml\Dom\Traverser\Visitor;

abstract class AbstractVisitor implements Visitor
{
    public function onNodeEnter(DOMNode $node): Action
    {
        return new Action\Noop();
    }

    public function onNodeLeave(DOMNode $node): Action
    {
        return new Action\Noop();
    }
}
