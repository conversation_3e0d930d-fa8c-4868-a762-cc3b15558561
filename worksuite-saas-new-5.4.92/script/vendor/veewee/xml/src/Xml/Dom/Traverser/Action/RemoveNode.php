<?php

declare(strict_types=1);

namespace Vee<PERSON>ee\Xml\Dom\Traverser\Action;

use DOMNode;
use Vee<PERSON>ee\Xml\Dom\Traverser\Action;
use Vee<PERSON>ee\Xml\Exception\RuntimeException;
use function VeeWee\Xml\Dom\Manipulator\Node\remove;

final class RemoveNode implements Action
{
    /**
     * @throws RuntimeException
     */
    public function __invoke(DOMNode $currentNode): void
    {
        remove($currentNode);
    }
}
