---
- name: Setup Cloudflare Tunnel for WorkSuite SAAS
  hosts: worksuite-production
  become: yes
  vars:
    cloudflare_token: "eyJhIjoiZmJiMDdkZGY2OTIxMjk3YWRiZWMwOGE5ODUzODZlODQiLCJ0IjoiYmI4OGYwYzYtOTA4Ny00NTZmLWI5ZWMtNDIwZjJhNGViYjk1IiwicyI6IllXRTNZekl4WXpndE16VXhaUzAwTkRVMUxXSmpaREl0TkRreE1tTTRaV0l4T0RRMSJ9"
    cloudflare_hostname: "erp.iti.id.vn"
    local_service: "https://localhost:8643"
    tunnel_name: "worksuite-tunnel"

  tasks:
    - name: Create cloudflared directory
      file:
        path: /etc/cloudflared
        state: directory
        mode: '0755'

    - name: Download cloudflared binary
      get_url:
        url: "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64"
        dest: /usr/local/bin/cloudflared
        mode: '0755'
        owner: root
        group: root

    - name: Create cloudflared configuration file
      template:
        src: cloudflared-config.yml.j2
        dest: /etc/cloudflared/config.yml
        mode: '0600'
        owner: root
        group: root
      notify: restart cloudflared

    - name: Create cloudflared systemd service file
      template:
        src: cloudflared.service.j2
        dest: /etc/systemd/system/cloudflared.service
        mode: '0644'
        owner: root
        group: root
      notify:
        - reload systemd
        - restart cloudflared

    - name: Create tunnel credentials file
      copy:
        content: "{{ cloudflare_token }}"
        dest: /etc/cloudflared/tunnel-token.txt
        mode: '0600'
        owner: root
        group: root

    - name: Enable and start cloudflared service
      systemd:
        name: cloudflared
        enabled: yes
        state: started
        daemon_reload: yes

    - name: Wait for tunnel to establish connection
      pause:
        seconds: 10

    - name: Check cloudflared service status
      systemd:
        name: cloudflared
      register: cloudflared_status

    - name: Display service status
      debug:
        msg: "Cloudflared service is {{ cloudflared_status.status.ActiveState }}"

    - name: Test tunnel connectivity
      uri:
        url: "https://{{ cloudflare_hostname }}"
        method: GET
        validate_certs: no
        timeout: 30
      register: tunnel_test
      ignore_errors: yes

    - name: Display tunnel test results
      debug:
        msg: |
          Tunnel Status: {{ 'SUCCESS' if tunnel_test.status == 200 else 'FAILED' }}
          Response Code: {{ tunnel_test.status | default('N/A') }}
          Public URL: https://{{ cloudflare_hostname }}
          Local Service: {{ local_service }}

  handlers:
    - name: reload systemd
      systemd:
        daemon_reload: yes

    - name: restart cloudflared
      systemd:
        name: cloudflared
        state: restarted
