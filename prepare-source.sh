#!/bin/bash

# Script to prepare WorkSuite SAAS source code with vendor dependencies
# Run this on your local machine before deployment

set -e

echo "🔧 Preparing WorkSuite SAAS Source Code"
echo "======================================="

SOURCE_DIR="worksuite-saas-new-5.4.92"

# Check if source directory exists
if [ ! -d "$SOURCE_DIR" ]; then
    echo "❌ Error: Source directory $SOURCE_DIR not found!"
    echo ""
    echo "Please ensure you have extracted the WorkSuite source code:"
    echo "  unzip worksuite-saas-new-5.4.92.zip"
    echo ""
    exit 1
fi

echo "✅ Source directory found: $SOURCE_DIR"

# Check if composer.json exists
if [ ! -f "$SOURCE_DIR/composer.json" ]; then
    echo "❌ Error: composer.json not found in $SOURCE_DIR"
    echo "This doesn't appear to be a valid Laravel/WorkSuite project."
    exit 1
fi

echo "✅ composer.json found"

# Check if vendor directory already exists
if [ -d "$SOURCE_DIR/vendor" ]; then
    echo "✅ Vendor directory already exists"
    
    # Check if vendor has content
    VENDOR_COUNT=$(find "$SOURCE_DIR/vendor" -name "*.php" | wc -l)
    if [ "$VENDOR_COUNT" -gt 100 ]; then
        echo "✅ Vendor directory appears to be populated ($VENDOR_COUNT PHP files found)"
        echo ""
        echo "🎉 Source code is ready for deployment!"
        echo ""
        echo "Next steps:"
        echo "  1. Run: ./deploy-no-composer.sh"
        echo "  2. Or run: ansible-playbook -i inventory/hosts.yml deploy-full-source.yml"
        echo ""
        exit 0
    else
        echo "⚠️  Vendor directory exists but appears empty"
        echo "Will reinstall dependencies..."
        rm -rf "$SOURCE_DIR/vendor"
    fi
fi

# Check if composer is installed
if ! command -v composer &> /dev/null; then
    echo "❌ Error: Composer is not installed!"
    echo ""
    echo "Please install Composer first:"
    echo "  # On macOS:"
    echo "  brew install composer"
    echo ""
    echo "  # On Ubuntu/Debian:"
    echo "  sudo apt update"
    echo "  sudo apt install composer"
    echo ""
    echo "  # Or download from: https://getcomposer.org/download/"
    echo ""
    exit 1
fi

echo "✅ Composer found: $(composer --version)"

# Check PHP version
PHP_VERSION=$(php -r "echo PHP_VERSION;")
echo "✅ PHP version: $PHP_VERSION"

# Navigate to source directory
cd "$SOURCE_DIR"

echo ""
echo "📦 Installing Composer Dependencies..."
echo "======================================"

# Remove existing vendor and lock file for clean install
rm -rf vendor/
rm -f composer.lock

# Install dependencies
echo "Running: composer install --no-dev --optimize-autoloader"
composer install --no-dev --optimize-autoloader

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Dependencies installed successfully!"
    
    # Check vendor directory
    VENDOR_COUNT=$(find vendor -name "*.php" | wc -l)
    echo "✅ Vendor directory created with $VENDOR_COUNT PHP files"
    
    # Check some important packages
    echo ""
    echo "📋 Checking Important Packages:"
    
    if [ -d "vendor/laravel/framework" ]; then
        echo "  ✅ Laravel Framework"
    else
        echo "  ❌ Laravel Framework - Missing!"
    fi
    
    if [ -d "vendor/froiden" ]; then
        echo "  ✅ Froiden Packages"
    else
        echo "  ❌ Froiden Packages - Missing!"
    fi
    
    if [ -d "vendor/symfony" ]; then
        echo "  ✅ Symfony Components"
    else
        echo "  ❌ Symfony Components - Missing!"
    fi
    
    echo ""
    echo "🎉 Source Code Preparation Complete!"
    echo "===================================="
    echo ""
    echo "Your source code is now ready for deployment with all dependencies included."
    echo ""
    echo "Next steps:"
    echo "  1. Go back to parent directory: cd .."
    echo "  2. Run deployment: ./deploy-no-composer.sh"
    echo ""
    
else
    echo ""
    echo "❌ Failed to install dependencies!"
    echo "================================="
    echo ""
    echo "Please check the error messages above and try again."
    echo "Common issues:"
    echo "  - PHP version compatibility"
    echo "  - Missing PHP extensions"
    echo "  - Network connectivity issues"
    echo "  - Insufficient disk space"
    echo ""
    exit 1
fi
