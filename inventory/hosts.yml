---
all:
  children:
    worksuite_servers:
      hosts:
        worksuite-production:
          ansible_host: ec2-52-77-233-254.ap-southeast-1.compute.amazonaws.com
          ansible_user: ubuntu
          ansible_ssh_private_key_file: ./thaodoiti.pem
          ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
          
          # Server Configuration
          server_name: worksuite-production
          server_environment: production
          
          # Application Configuration
          app_name: "WorkSuite SAAS"
          app_env: production
          app_debug: false
          app_url: "http://{{ ansible_host }}:8601"
          app_url_https: "https://{{ ansible_host }}:8643"
          
          # Web Server Configuration
          nginx_http_port: 8601
          nginx_https_port: 8643
          nginx_server_name: "{{ ansible_host }}"
          
          # Database Configuration
          mysql_root_password: "WS_RootPass_2024_Secure!"
          mysql_database: worksuite_saas
          mysql_user: worksuite_user
          mysql_password: "WS_UserPass_2024_Secure!"
          
          # Redis Configuration
          redis_password: "WS_RedisPass_2024_Secure!"
          redis_port: 6379
          
          # PHP Configuration
          php_version: "8.2"
          php_memory_limit: "512M"
          php_max_execution_time: 300
          php_upload_max_filesize: "100M"
          php_post_max_size: "100M"
          
          # SSL Configuration
          ssl_cert_path: "/etc/ssl/certs/worksuite.crt"
          ssl_key_path: "/etc/ssl/private/worksuite.key"
          ssl_country: "SG"
          ssl_state: "Singapore"
          ssl_city: "Singapore"
          ssl_organization: "WorkSuite SAAS"
          ssl_organizational_unit: "IT Department"
          ssl_common_name: "{{ ansible_host }}"
          
          # Application Paths
          app_root: "/var/www/worksuite"
          app_public: "{{ app_root }}/public"
          app_storage: "{{ app_root }}/storage"
          app_bootstrap_cache: "{{ app_root }}/bootstrap/cache"
          
          # System User
          app_user: worksuite
          app_group: www-data
          
      vars:
        # Global variables for all worksuite servers
        ansible_python_interpreter: /usr/bin/python3
        ansible_become: yes
        ansible_become_method: sudo
        
        # Timezone
        system_timezone: "Asia/Singapore"
        
        # Security
        ufw_enabled: true
        fail2ban_enabled: true
        
        # Backup Configuration
        backup_enabled: true
        backup_retention_days: 7
        backup_path: "/var/backups/worksuite"
