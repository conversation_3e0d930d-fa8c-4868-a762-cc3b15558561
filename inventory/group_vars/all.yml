---
# Global variables for all hosts

# System Configuration
system_packages:
  - curl
  - wget
  - unzip
  - git
  - htop
  - vim
  - nano
  - tree
  - software-properties-common
  - apt-transport-https
  - ca-certificates
  - gnupg
  - lsb-release
  - ufw
  - fail2ban

# PHP Configuration
php_packages:
  - "php{{ php_version }}-fpm"
  - "php{{ php_version }}-cli"
  - "php{{ php_version }}-mysql"
  - "php{{ php_version }}-redis"
  - "php{{ php_version }}-mbstring"
  - "php{{ php_version }}-xml"
  - "php{{ php_version }}-zip"
  - "php{{ php_version }}-curl"
  - "php{{ php_version }}-gd"
  - "php{{ php_version }}-intl"
  - "php{{ php_version }}-soap"
  - "php{{ php_version }}-bcmath"
  - "php{{ php_version }}-imagick"
  - "php{{ php_version }}-opcache"

# MariaDB Configuration
mariadb_version: "10.11"
mariadb_packages:
  - mariadb-server
  - mariadb-client
  - python3-pymysql

# Nginx Configuration
nginx_packages:
  - nginx
  - nginx-common

# Redis Configuration
redis_packages:
  - redis-server
  - redis-tools

# Composer Configuration
composer_url: "https://getcomposer.org/installer"
composer_path: "/usr/local/bin/composer"

# Node.js Configuration (for asset compilation if needed)
nodejs_version: "18"

# Firewall Configuration
firewall_allowed_ports:
  - "22/tcp"    # SSH
  - "{{ nginx_http_port }}/tcp"   # HTTP
  - "{{ nginx_https_port }}/tcp"  # HTTPS

# Log Configuration
log_rotation_enabled: true
log_retention_days: 30

# Monitoring Configuration
monitoring_enabled: false
monitoring_tools: []

# Backup Configuration
backup_schedule: "0 2 * * *"  # Daily at 2 AM
backup_mysql_enabled: true
backup_files_enabled: true

# Security Configuration
security_updates_enabled: true
automatic_updates_enabled: false

# Performance Configuration
swap_file_size: "2G"
swap_file_path: "/swapfile"
