# WorkSuite SAAS Deployment - No Composer Mode

Hướng dẫn deploy WorkSuite SAAS bằng cách copy nguyên cả bộ source code (bao gồm vendor) thay vì sử dụng composer install/update.

## 📋 Yêu Cầu

### 1. Source Code Đầy Đủ
- C<PERSON>n có bộ source code WorkSuite SAAS đầy đủ
- **<PERSON>uan trọng**: Source code phải bao gồm thư mục `vendor/` với tất cả dependencies
- Thư mục source phải có tên: `worksuite-saas-new-5.4.92`

### 2. Cấu Trúc Th<PERSON>
```
project-directory/
├── worksuite-saas-new-5.4.92/          # Source code đầy đủ
│   ├── app/
│   ├── vendor/                          # ← Quan trọng: phải có thư mục này
│   ├── public/
│   ├── storage/
│   ├── composer.json
│   └── ...
├── inventory/
│   └── hosts.yml
├── templates/
│   └── .env.j2
├── deploy-full-source.yml
├── tasks/
│   └── disable-license-check.yml
└── deploy-no-composer.sh
```

## 🚀 Cách Sử Dụng

### Bước 1: Chuẩn Bị Source Code
```bash
# Giải nén source code WorkSuite SAAS
unzip worksuite-saas-new-5.4.92.zip

# Kiểm tra thư mục vendor có tồn tại không
ls -la worksuite-saas-new-5.4.92/vendor/

# Nếu không có vendor, cần chạy composer trên máy local trước
cd worksuite-saas-new-5.4.92/
composer install --no-dev --optimize-autoloader
cd ..
```

### Bước 2: Chạy Deployment
```bash
# Cấp quyền thực thi cho script
chmod +x deploy-no-composer.sh

# Chạy deployment
./deploy-no-composer.sh
```

## 🔧 Tính Năng

### ✅ Ưu Điểm của Phương Pháp Này
1. **Nhanh hơn**: Không cần download dependencies từ internet
2. **Ổn định hơn**: Không phụ thuộc vào Packagist hay các repo external
3. **Offline deployment**: Có thể deploy mà không cần internet
4. **Version lock**: Đảm bảo sử dụng đúng version dependencies đã test

### ✅ Những Gì Script Sẽ Làm
1. **Backup**: Tạo backup của deployment hiện tại
2. **Copy Source**: Copy toàn bộ source code lên server
3. **Permissions**: Set đúng quyền cho files và directories
4. **Configuration**: Tạo file .env từ template
5. **License Bypass**: Tự động disable tất cả license checks
6. **Services**: Restart các services cần thiết
7. **Testing**: Test xem application có hoạt động không

## 📁 Files Quan Trọng

### `deploy-full-source.yml`
- Ansible playbook chính
- Xử lý việc copy source code và cấu hình

### `tasks/disable-license-check.yml`
- Tasks để disable license verification
- Modify các file cần thiết để bypass license

### `deploy-no-composer.sh`
- Script wrapper để chạy deployment
- Kiểm tra prerequisites và chạy playbook

## 🛠️ Troubleshooting

### Lỗi: Source directory not found
```bash
# Đảm bảo thư mục source có đúng tên
mv your-source-folder worksuite-saas-new-5.4.92
```

### Lỗi: Vendor directory not found
```bash
# Chạy composer install trên máy local trước
cd worksuite-saas-new-5.4.92/
composer install --no-dev --optimize-autoloader
```

### Lỗi: Permission denied
```bash
# Đảm bảo có quyền SSH và sudo
ansible all -i inventory/hosts.yml -m ping
```

## 📝 Sau Khi Deploy

### 1. Kiểm Tra Application
- Truy cập: https://erp.iti.id.vn
- Login: <EMAIL> / password123

### 2. Cấu Hình Bổ Sung
- Tạo thêm users
- Cấu hình company settings
- Setup email, payment gateway, etc.

### 3. Backup Strategy
- Script tự động tạo backup trước khi deploy
- Backup được lưu tại `/tmp/worksuite-backup-[timestamp].tar.gz`

## 🔒 Security Notes

- License verification đã được disable hoàn toàn
- Application sẽ không check license nữa
- Tất cả package validation functions return true
- Route verify-purchase redirect về dashboard

## 📞 Support

Nếu gặp vấn đề trong quá trình deployment:
1. Kiểm tra log của Ansible
2. Kiểm tra log của Nginx và PHP-FPM
3. Kiểm tra file .env có đúng không
4. Kiểm tra database connection
