---
# Tasks to disable license checking in WorkSuite SAAS

- name: Create backup of license-related files
  copy:
    src: "{{ item }}"
    dest: "{{ item }}.backup"
    remote_src: yes
  loop:
    - "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php"
    - "{{ app_path }}/app/Http/Middleware/CheckCompanyPackage.php"
    - "{{ app_path }}/app/Helper/start.php"
  ignore_errors: yes

- name: Modify AppBoot.php to disable license checking
  replace:
    path: "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php"
    regexp: 'public function isLegal\(\)'
    replace: 'public function isLegal() { return true; } public function isLegal_old()'

- name: Modify CheckCompanyPackage middleware
  replace:
    path: "{{ app_path }}/app/Http/Middleware/CheckCompanyPackage.php"
    regexp: 'public function handle\(Request \$request, Closure \$next\): Response\s*\{[\s\S]*?return \$next\(\$request\);\s*\}'
    replace: |
      public function handle(Request $request, Closure $next): Response
      {
          return $next($request);
      }

- name: Create license bypass helper file
  copy:
    content: |
      <?php
      
      // License bypass functions for WorkSuite SAAS
      if (!function_exists('checkCompanyPackageIsValid')) {
          function checkCompanyPackageIsValid($companyId) {
              return true;
          }
      }
      
      if (!function_exists('checkCompanyCanAddMoreEmployees')) {
          function checkCompanyCanAddMoreEmployees($companyId) {
              return true;
          }
      }
      
      if (!function_exists('clearCompanyValidPackageCache')) {
          function clearCompanyValidPackageCache($companyId) {
              return true;
          }
      }
    dest: "{{ app_path }}/app/Helper/license_bypass.php"
    owner: "{{ web_user }}"
    group: "{{ web_group }}"
    mode: '0644'

- name: Add license bypass to composer autoload
  lineinfile:
    path: "{{ app_path }}/composer.json"
    regexp: '"app/Helper/start.php"'
    line: '            "app/Helper/start.php",'
    insertafter: '"files": \['

- name: Add license bypass file to autoload
  lineinfile:
    path: "{{ app_path }}/composer.json"
    line: '            "app/Helper/license_bypass.php"'
    insertafter: '"app/Helper/start.php",'

- name: Regenerate autoload files
  shell: |
    cd {{ app_path }}
    sudo -u {{ web_user }} composer dump-autoload --no-dev --optimize
  ignore_errors: yes

- name: Add bypass route for verify-purchase
  lineinfile:
    path: "{{ app_path }}/routes/web.php"
    line: |
      
      // Custom route to bypass license verification
      Route::get('verify-purchase', function() {
          return redirect()->route('dashboard');
      })->name('verify-purchase-bypass');
    insertafter: "<?php"

- name: Clear application caches after license bypass
  shell: |
    cd {{ app_path }}
    sudo -u {{ web_user }} php artisan cache:clear
    sudo -u {{ web_user }} php artisan config:clear
    sudo -u {{ web_user }} php artisan route:clear
  ignore_errors: yes
