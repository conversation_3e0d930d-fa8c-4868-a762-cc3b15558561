#!/usr/bin/env python3

import re
import sys

def fix_appboot_file(file_path):
    """Fix the AppBoot.php file to disable license checking"""
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Replace shouldSkipLicenseCheck function to always return true
        pattern = r'private function shouldSkipLicenseCheck\(\): bool\s*\{[^}]*\}'
        replacement = '''private function shouldSkipLicenseCheck(): bool
    {
        return true;
    }'''
        
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # Also modify isLegal to return true early
        pattern2 = r'(public function isLegal\(\)\s*\{)'
        replacement2 = r'\1\n        return true;'
        
        content = re.sub(pattern2, replacement2, content)
        
        # Write back to file
        with open(file_path, 'w') as f:
            f.write(content)
            
        print("Successfully modified AppBoot.php")
        return True
        
    except Exception as e:
        print(f"Error modifying file: {e}")
        return False

def fix_middleware_file(file_path):
    """Fix the CheckCompanyPackage middleware"""
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Replace the handle method to just pass through
        pattern = r'public function handle\(Request \$request, Closure \$next\): Response\s*\{[^}]*return \$next\(\$request\);\s*\}'
        replacement = '''public function handle(Request $request, Closure $next): Response
    {
        return $next($request);
    }'''
        
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        with open(file_path, 'w') as f:
            f.write(content)
            
        print("Successfully modified CheckCompanyPackage.php")
        return True
        
    except Exception as e:
        print(f"Error modifying middleware: {e}")
        return False

def fix_helper_functions(file_path):
    """Fix helper functions to always return true"""
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Fix checkCompanyPackageIsValid function
        pattern1 = r'function checkCompanyPackageIsValid\([^)]*\)\s*\{[^}]*\}'
        replacement1 = '''function checkCompanyPackageIsValid($companyId)
    {
        return true;
    }'''
        
        content = re.sub(pattern1, replacement1, content, flags=re.DOTALL)
        
        # Fix checkCompanyCanAddMoreEmployees function
        pattern2 = r'function checkCompanyCanAddMoreEmployees\([^)]*\)\s*\{[^}]*\}'
        replacement2 = '''function checkCompanyCanAddMoreEmployees($companyId)
    {
        return true;
    }'''
        
        content = re.sub(pattern2, replacement2, content, flags=re.DOTALL)
        
        with open(file_path, 'w') as f:
            f.write(content)
            
        print("Successfully modified helper functions")
        return True
        
    except Exception as e:
        print(f"Error modifying helper functions: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 fix-license-check.py <action>")
        print("Actions: appboot, middleware, helper")
        sys.exit(1)
    
    action = sys.argv[1]
    
    if action == "appboot":
        success = fix_appboot_file("/var/www/worksuite/vendor/froiden/envato/src/Traits/AppBoot.php")
    elif action == "middleware":
        success = fix_middleware_file("/var/www/worksuite/app/Http/Middleware/CheckCompanyPackage.php")
    elif action == "helper":
        success = fix_helper_functions("/var/www/worksuite/app/Helper/start.php")
    else:
        print("Invalid action")
        sys.exit(1)
    
    sys.exit(0 if success else 1)
