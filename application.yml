---
- name: Deploy WorkSuite SAAS Application
  hosts: worksuite_servers
  become: yes
  gather_facts: yes
  
  vars:
    source_path: "{{ playbook_dir }}/worksuite-saas-new-5.4.92/script"
    
  tasks:
    - name: Create application directories
      file:
        path: "{{ item }}"
        state: directory
        owner: "{{ app_user }}"
        group: "{{ app_group }}"
        mode: '0755'
      loop:
        - "{{ app_root }}"
        - "{{ app_storage }}"
        - "{{ app_storage }}/logs"
        - "{{ app_storage }}/framework"
        - "{{ app_storage }}/framework/cache"
        - "{{ app_storage }}/framework/sessions"
        - "{{ app_storage }}/framework/views"
        - "{{ app_bootstrap_cache }}"
        - "/var/log/worksuite"
        - "/var/backups/worksuite"
      tags: [deploy, directories]

    - name: Check if application source exists
      local_action:
        module: stat
        path: "{{ source_path }}"
      register: source_check
      become: no
      tags: [deploy, check]

    - name: Fail if source directory doesn't exist
      fail:
        msg: "Source directory {{ source_path }} does not exist. Please ensure worksuite-saas-new-5.4.92/script is present."
      when: not source_check.stat.exists
      tags: [deploy, check]

    - name: Synchronize application files
      synchronize:
        src: "{{ source_path }}/"
        dest: "{{ app_root }}/"
        delete: no
        recursive: yes
        rsync_opts:
          - "--exclude=.git"
          - "--exclude=.env"
          - "--exclude=.env.*"
          - "--exclude=node_modules"
          - "--exclude=vendor"
          - "--exclude=storage/logs/*"
          - "--exclude=storage/framework/cache/*"
          - "--exclude=storage/framework/sessions/*"
          - "--exclude=storage/framework/views/*"
          - "--exclude=bootstrap/cache/*"
      tags: [deploy, sync]

    - name: Set application file ownership
      file:
        path: "{{ app_root }}"
        owner: "{{ app_user }}"
        group: "{{ app_group }}"
        recurse: yes
      tags: [deploy, permissions]

    - name: Set storage and cache permissions
      file:
        path: "{{ item }}"
        mode: '0775'
        recurse: yes
      loop:
        - "{{ app_storage }}"
        - "{{ app_bootstrap_cache }}"
      tags: [deploy, permissions]

    - name: Create application environment file
      template:
        src: templates/app.env.j2
        dest: "{{ app_root }}/.env"
        owner: "{{ app_user }}"
        group: "{{ app_group }}"
        mode: '0640'
      tags: [deploy, config]

    - name: Install Composer dependencies
      composer:
        command: install
        working_dir: "{{ app_root }}"
        no_dev: yes
        optimize_autoloader: yes
      become_user: "{{ app_user }}"
      tags: [deploy, composer]

    - name: Generate application key
      command: php artisan key:generate --force
      args:
        chdir: "{{ app_root }}"
      become_user: "{{ app_user }}"
      tags: [deploy, laravel]

    - name: Clear application cache
      command: "{{ item }}"
      args:
        chdir: "{{ app_root }}"
      become_user: "{{ app_user }}"
      loop:
        - php artisan config:clear
        - php artisan cache:clear
        - php artisan route:clear
        - php artisan view:clear
      tags: [deploy, laravel]

    - name: Run database migrations
      command: php artisan migrate --force
      args:
        chdir: "{{ app_root }}"
      become_user: "{{ app_user }}"
      tags: [deploy, database]

    - name: Optimize application
      command: "{{ item }}"
      args:
        chdir: "{{ app_root }}"
      become_user: "{{ app_user }}"
      loop:
        - php artisan config:cache
        - php artisan route:cache
        - php artisan view:cache
      tags: [deploy, optimize]

    - name: Create Laravel queue systemd service
      template:
        src: templates/laravel-queue.service.j2
        dest: /etc/systemd/system/laravel-queue.service
      notify: 
        - reload systemd
        - restart laravel-queue
      tags: [deploy, queue]

    - name: Create Laravel scheduler systemd service
      template:
        src: templates/laravel-scheduler.service.j2
        dest: /etc/systemd/system/laravel-scheduler.service
      notify:
        - reload systemd
        - restart laravel-scheduler
      tags: [deploy, scheduler]

    - name: Create Laravel scheduler timer
      template:
        src: templates/laravel-scheduler.timer.j2
        dest: /etc/systemd/system/laravel-scheduler.timer
      notify:
        - reload systemd
        - restart laravel-scheduler-timer
      tags: [deploy, scheduler]

    - name: Enable and start Laravel services
      systemd:
        name: "{{ item }}"
        enabled: yes
        state: started
        daemon_reload: yes
      loop:
        - laravel-queue
        - laravel-scheduler.timer
      tags: [deploy, services]

    - name: Create log rotation configuration
      template:
        src: templates/worksuite-logrotate.j2
        dest: /etc/logrotate.d/worksuite
      tags: [deploy, logs]

    - name: Create backup script
      template:
        src: templates/backup-worksuite.sh.j2
        dest: /usr/local/bin/backup-worksuite.sh
        mode: '0755'
      tags: [deploy, backup]

    - name: Create backup cron job
      cron:
        name: "WorkSuite SAAS Backup"
        cron_file: worksuite-backup
        minute: "0"
        hour: "2"
        job: "/usr/local/bin/backup-worksuite.sh"
        user: root
      when: backup_enabled | default(true)
      tags: [deploy, backup]

    - name: Verify application is working
      uri:
        url: "http://localhost:{{ nginx_http_port }}"
        method: GET
        status_code: 200
      register: app_check
      retries: 3
      delay: 10
      tags: [deploy, verify]

  handlers:
    - name: reload systemd
      systemd:
        daemon_reload: yes

    - name: restart laravel-queue
      systemd:
        name: laravel-queue
        state: restarted

    - name: restart laravel-scheduler
      systemd:
        name: laravel-scheduler
        state: restarted

    - name: restart laravel-scheduler-timer
      systemd:
        name: laravel-scheduler.timer
        state: restarted
