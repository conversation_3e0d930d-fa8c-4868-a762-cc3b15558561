---
- name: Setup SSL Certificates and Configure Nginx
  hosts: worksuite_servers
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Install OpenSSL
      apt:
        name: openssl
        state: present
      tags: [ssl, packages]

    - name: Create SSL directories
      file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - /etc/ssl/certs
        - /etc/ssl/private
      tags: [ssl, directories]

    - name: Generate private key
      openssl_privatekey:
        path: "{{ ssl_key_path }}"
        size: 2048
        mode: '0600'
      tags: [ssl, key]

    - name: Generate certificate signing request
      openssl_csr:
        path: /tmp/worksuite.csr
        privatekey_path: "{{ ssl_key_path }}"
        country_name: "{{ ssl_country }}"
        state_or_province_name: "{{ ssl_state }}"
        locality_name: "{{ ssl_city }}"
        organization_name: "{{ ssl_organization }}"
        organizational_unit_name: "{{ ssl_organizational_unit }}"
        common_name: "{{ ssl_common_name }}"
        subject_alt_name:
          - "DNS:{{ ssl_common_name }}"
          - "DNS:localhost"
          - "IP:127.0.0.1"
          - "IP:{{ ansible_default_ipv4.address }}"
      tags: [ssl, csr]

    - name: Generate self-signed certificate
      openssl_certificate:
        path: "{{ ssl_cert_path }}"
        privatekey_path: "{{ ssl_key_path }}"
        csr_path: /tmp/worksuite.csr
        provider: selfsigned
        mode: '0644'
        selfsigned_not_after: "+365d"
      tags: [ssl, cert]

    - name: Remove CSR file
      file:
        path: /tmp/worksuite.csr
        state: absent
      tags: [ssl, cleanup]

    - name: Configure Nginx HTTP virtual host
      template:
        src: templates/nginx-worksuite-http.conf.j2
        dest: /etc/nginx/sites-available/worksuite-http
        backup: yes
      notify: restart nginx
      tags: [nginx, config]

    - name: Configure Nginx HTTPS virtual host
      template:
        src: templates/nginx-worksuite-https.conf.j2
        dest: /etc/nginx/sites-available/worksuite-https
        backup: yes
      notify: restart nginx
      tags: [nginx, config]

    - name: Enable Nginx HTTP site
      file:
        src: /etc/nginx/sites-available/worksuite-http
        dest: /etc/nginx/sites-enabled/worksuite-http
        state: link
      notify: restart nginx
      tags: [nginx, enable]

    - name: Enable Nginx HTTPS site
      file:
        src: /etc/nginx/sites-available/worksuite-https
        dest: /etc/nginx/sites-enabled/worksuite-https
        state: link
      notify: restart nginx
      tags: [nginx, enable]

    - name: Test Nginx configuration
      command: nginx -t
      register: nginx_test
      changed_when: false
      tags: [nginx, test]

    - name: Display Nginx test results
      debug:
        var: nginx_test.stdout_lines
      tags: [nginx, test]

    - name: Ensure Nginx is running
      systemd:
        name: nginx
        state: started
        enabled: yes
      tags: [nginx, service]

    - name: Create SSL certificate info script
      template:
        src: templates/ssl-info.sh.j2
        dest: /usr/local/bin/ssl-info.sh
        mode: '0755'
      tags: [ssl, info]

    - name: Display SSL certificate information
      command: /usr/local/bin/ssl-info.sh
      register: ssl_info
      changed_when: false
      tags: [ssl, info]

    - name: Show SSL certificate details
      debug:
        var: ssl_info.stdout_lines
      tags: [ssl, info]

  handlers:
    - name: restart nginx
      systemd:
        name: nginx
        state: restarted
