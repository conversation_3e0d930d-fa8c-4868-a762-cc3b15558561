<?php

// <PERSON><PERSON><PERSON> to create test user for WorkSuite SAAS
require_once '/var/www/worksuite/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = new Application(realpath('/var/www/worksuite'));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    // Check if companies table exists and has data
    $companies = DB::table('companies')->get();
    
    if ($companies->isEmpty()) {
        echo "Creating test company...\n";
        
        // Create a test company first
        $companyId = DB::table('companies')->insertGetId([
            'company_name' => 'Test Company',
            'company_email' => '<EMAIL>',
            'company_phone' => '1234567890',
            'website' => 'https://test.com',
            'address' => 'Test Address',
            'status' => 'active',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        echo "Company created with ID: $companyId\n";
    } else {
        $companyId = $companies->first()->id;
        echo "Using existing company ID: $companyId\n";
    }
    
    // Check if user already exists
    $existingUser = DB::table('users')->where('email', '<EMAIL>')->first();
    
    if ($existingUser) {
        echo "User <EMAIL> already exists!\n";
        echo "You can login with: <EMAIL> / password123\n";
    } else {
        // Create test user
        $userId = DB::table('users')->insertGetId([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_id' => $companyId,
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        echo "Test user created successfully!\n";
        echo "Email: <EMAIL>\n";
        echo "Password: password123\n";
        echo "User ID: $userId\n";
        
        // Assign admin role if roles table exists
        try {
            $adminRole = DB::table('roles')->where('name', 'admin')->first();
            if ($adminRole) {
                DB::table('role_user')->insert([
                    'user_id' => $userId,
                    'role_id' => $adminRole->id,
                ]);
                echo "Admin role assigned!\n";
            }
        } catch (Exception $e) {
            echo "Note: Could not assign role (table may not exist)\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
