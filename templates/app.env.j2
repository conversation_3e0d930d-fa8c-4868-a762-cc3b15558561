# WorkSuite SAAS Environment Configuration
# Generated by Ansible on {{ ansible_date_time.iso8601 }}

# Application Configuration
APP_NAME="{{ app_name }}"
APP_ENV={{ app_env }}
APP_KEY=base64:{{ ansible_password_hash | default('') }}
APP_DEBUG={{ app_debug | lower }}
APP_URL={{ app_url }}

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE={{ mysql_database }}
DB_USERNAME={{ mysql_user }}
DB_PASSWORD={{ mysql_password }}

# Cache Configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120
QUEUE_CONNECTION=redis

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT={{ redis_port }}
REDIS_PASSWORD=

# Mail Configuration (Update with your SMTP settings)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# File Storage Configuration
FILESYSTEM_DISK=local

# Logging Configuration
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Broadcasting Configuration
BROADCAST_DRIVER=log
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Security Settings
SANCTUM_STATEFUL_DOMAINS={{ nginx_server_name }}:{{ nginx_http_port }}
SESSION_DOMAIN={{ nginx_server_name }}

# Additional Laravel Settings
BCRYPT_ROUNDS=10
HASH_DRIVER=bcrypt

# Timezone
APP_TIMEZONE={{ system_timezone }}

# Locale
APP_LOCALE=en
APP_FALLBACK_LOCALE=en

# Maintenance Mode
APP_MAINTENANCE_DRIVER=file

# Queue Configuration
QUEUE_FAILED_DRIVER=database-uuids

# Asset URL (for CDN if needed)
ASSET_URL=

# Mix Configuration
MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# JWT Configuration
JWT_SECRET=z6dl6gOuGtZj26ipPx5mgbHABkmCKNrqBwZON555Rx96UR7jDl7iuO5ONZoldMj5

# Additional WorkSuite specific settings
REDIRECT_HTTPS=false
MAIL_FROM_VERIFIED_EMAIL=true
MAIN_APPLICATION_SUBDOMAIN=
