# WorkSuite SAAS Security Audit Report
Generated: {{ ansible_date_time.iso8601 }}
Server: {{ ansible_hostname }} ({{ ansible_default_ipv4.address }})

## System Information
- OS: {{ ansible_distribution }} {{ ansible_distribution_version }}
- Kernel: {{ ansible_kernel }}
- Architecture: {{ ansible_architecture }}
- Uptime: {{ ansible_uptime_seconds }} seconds

## Security Updates
{% if security_updates.stdout_lines %}
Available Security Updates:
{% for update in security_updates.stdout_lines %}
- {{ update }}
{% endfor %}
{% else %}
✅ No security updates available
{% endif %}

## Firewall Configuration
Status: {{ firewall_status.state | default('Unknown') }}

Current Rules:
{{ firewall_rules.stdout }}

## SSH Security Configuration
{% if ssh_config_changes.changed %}
✅ SSH security settings updated:
{% for item in ssh_config_changes.results %}
{% if item.changed %}
- {{ item.item.line }}
{% endif %}
{% endfor %}
{% else %}
✅ SSH already configured securely
{% endif %}

## Failed Login Attempts (Last 10)
{% if failed_logins.stdout_lines %}
{% for login in failed_logins.stdout_lines %}
{{ login }}
{% endfor %}
{% else %}
✅ No recent failed login attempts
{% endif %}

## Running Services
{{ running_services.stdout }}

## Listening Ports
{{ listening_ports.stdout }}

## File Permissions Audit
{% for item in file_permissions.results %}
{{ item.item }}: {{ item.stat.mode }} ({{ item.stat.pw_name }}:{{ item.stat.gr_name }})
{% endfor %}

## Database Users
{% if db_users.query_result is defined %}
{% for user in db_users.query_result %}
- {{ user[0] }}@{{ user[1] }}
{% endfor %}
{% else %}
⚠️ Could not retrieve database user information
{% endif %}

## PHP Security Configuration
{% if php_security_config.changed %}
✅ PHP security settings updated
{% else %}
✅ PHP already configured securely
{% endif %}

## Nginx Security Headers
{{ nginx_security_headers.stdout | default('No security headers found') }}

## World-Writable Files
{% if world_writable_files.stdout_lines %}
⚠️ World-writable files found:
{% for file in world_writable_files.stdout_lines %}
{{ file }}
{% endfor %}
{% else %}
✅ No world-writable files found
{% endif %}

## Application Errors (Recent)
{% if app_errors.stdout_lines %}
⚠️ Recent application errors:
{% for error in app_errors.stdout_lines %}
{{ error }}
{% endfor %}
{% else %}
✅ No recent application errors
{% endif %}

## SSL Certificate Validity
{{ ssl_cert_validity.stdout }}

## Security Recommendations

### High Priority
1. **Keep system updated**: Regularly apply security updates
2. **Monitor failed logins**: Set up log monitoring for suspicious activity
3. **Review application errors**: Address any application-level security issues
4. **SSL certificates**: Consider using trusted CA certificates for production

### Medium Priority
1. **Implement log monitoring**: Set up centralized logging and alerting
2. **Regular security audits**: Run this audit monthly
3. **Backup verification**: Test backup and restore procedures
4. **Access control**: Implement additional access controls if needed

### Low Priority
1. **Performance monitoring**: Monitor application performance
2. **Documentation updates**: Keep security documentation current
3. **Security training**: Ensure team is aware of security best practices

## Compliance Status
- ✅ Firewall configured and enabled
- ✅ SSH hardened (key-based authentication only)
- ✅ PHP security settings configured
- ✅ File permissions properly set
- ✅ SSL/TLS encryption enabled
- ✅ Database access controlled
- ✅ Application logs monitored

## Next Security Review
Recommended: {{ (ansible_date_time.epoch | int + 2592000) | strftime('%Y-%m-%d') }} (30 days from now)
