[Unit]
Description=Laravel Scheduler for WorkSuite SAAS
After=network.target mariadb.service redis-server.service

[Service]
Type=oneshot
User={{ app_user }}
Group={{ app_group }}
WorkingDirectory={{ app_root }}
ExecStart=/usr/bin/php {{ app_root }}/artisan schedule:run
StandardOutput=journal
StandardError=journal
SyslogIdentifier=laravel-scheduler

# Environment
Environment=HOME=/home/<USER>
Environment=USER={{ app_user }}
