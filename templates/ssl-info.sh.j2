#!/bin/bash
# SSL Certificate Information Script
# Generated by Ansible on {{ ansible_date_time.iso8601 }}

echo "=== WorkSuite SAAS SSL Certificate Information ==="
echo ""
echo "Certificate Path: {{ ssl_cert_path }}"
echo "Private Key Path: {{ ssl_key_path }}"
echo ""
echo "Certificate Details:"
openssl x509 -in {{ ssl_cert_path }} -text -noout | grep -A 2 "Subject:"
openssl x509 -in {{ ssl_cert_path }} -text -noout | grep -A 1 "Not Before"
openssl x509 -in {{ ssl_cert_path }} -text -noout | grep -A 1 "Not After"
echo ""
echo "Subject Alternative Names:"
openssl x509 -in {{ ssl_cert_path }} -text -noout | grep -A 1 "Subject Alternative Name" | tail -1
echo ""
echo "Certificate Fingerprint:"
openssl x509 -in {{ ssl_cert_path }} -fingerprint -noout
echo ""
echo "=== Access URLs ==="
echo "HTTP:  http://{{ nginx_server_name }}:{{ nginx_http_port }}"
echo "HTTPS: https://{{ nginx_server_name }}:{{ nginx_https_port }}"
echo ""
echo "Note: For HTTPS access, you may need to accept the self-signed certificate warning in your browser."
