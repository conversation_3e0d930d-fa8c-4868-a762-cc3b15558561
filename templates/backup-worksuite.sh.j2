#!/bin/bash
# WorkSuite SAAS Backup Script
# Generated by Ansible on {{ ansible_date_time.iso8601 }}

set -e

# Configuration
BACKUP_DIR="{{ backup_path }}"
APP_ROOT="{{ app_root }}"
DB_NAME="{{ mysql_database }}"
DB_USER="{{ mysql_user }}"
DB_PASS="{{ mysql_password }}"
RETENTION_DAYS="{{ backup_retention_days | default(7) }}"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BACKUP_DIR/backup.log"
}

log "Starting WorkSuite SAAS backup..."

# Database backup
if [ "{{ backup_mysql_enabled | default(true) | lower }}" = "true" ]; then
    log "Backing up database..."
    mysqldump -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$BACKUP_DIR/database_$DATE.sql"
    gzip "$BACKUP_DIR/database_$DATE.sql"
    log "Database backup completed: database_$DATE.sql.gz"
fi

# Files backup
if [ "{{ backup_files_enabled | default(true) | lower }}" = "true" ]; then
    log "Backing up application files..."
    tar -czf "$BACKUP_DIR/files_$DATE.tar.gz" \
        -C "$(dirname $APP_ROOT)" \
        --exclude="$(basename $APP_ROOT)/storage/logs/*" \
        --exclude="$(basename $APP_ROOT)/storage/framework/cache/*" \
        --exclude="$(basename $APP_ROOT)/storage/framework/sessions/*" \
        --exclude="$(basename $APP_ROOT)/storage/framework/views/*" \
        --exclude="$(basename $APP_ROOT)/bootstrap/cache/*" \
        --exclude="$(basename $APP_ROOT)/vendor" \
        --exclude="$(basename $APP_ROOT)/node_modules" \
        "$(basename $APP_ROOT)"
    log "Files backup completed: files_$DATE.tar.gz"
fi

# Clean old backups
log "Cleaning old backups (older than $RETENTION_DAYS days)..."
find "$BACKUP_DIR" -name "database_*.sql.gz" -mtime +$RETENTION_DAYS -delete
find "$BACKUP_DIR" -name "files_*.tar.gz" -mtime +$RETENTION_DAYS -delete

# Backup summary
log "Backup completed successfully!"
log "Backup location: $BACKUP_DIR"
log "Files in backup directory:"
ls -lah "$BACKUP_DIR" | tail -n +2 | while read line; do
    log "  $line"
done

# Optional: Send notification (uncomment and configure as needed)
# echo "WorkSuite SAAS backup completed on $(hostname) at $(date)" | mail -s "Backup Completed" <EMAIL>

exit 0
