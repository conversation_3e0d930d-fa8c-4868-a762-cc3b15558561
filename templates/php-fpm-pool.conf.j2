[worksuite]
; Pool configuration for WorkSuite SAAS

; Unix user/group of processes
user = {{ app_user }}
group = {{ app_group }}

; The address on which to accept FastCGI requests
listen = /run/php/php{{ php_version }}-fpm-worksuite.sock

; Set listen(2) backlog
listen.backlog = 511

; Set permissions for unix socket
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

; Choose how the process manager will control the number of child processes
pm = dynamic

; The number of child processes to be created when pm is set to 'static'
pm.max_children = 20

; The number of child processes created on startup
pm.start_servers = 4

; The desired minimum number of idle server processes
pm.min_spare_servers = 2

; The desired maximum number of idle server processes
pm.max_spare_servers = 8

; The number of requests each child process should execute before respawning
pm.max_requests = 1000

; The URI to view the FPM status page
pm.status_path = /fpm-status

; The ping URI to call the monitoring page of FPM
ping.path = /fpm-ping

; This directive may be used to customize the response of a ping request
ping.response = pong

; The access log file
access.log = /var/log/php{{ php_version }}-fpm-worksuite.access.log

; The access log format
access.format = "%R - %u %t \"%m %r%Q%q\" %s %f %{mili}d %{kilo}M %C%%"

; The log file for slow requests
slowlog = /var/log/php{{ php_version }}-fpm-worksuite.slow.log

; The timeout for serving a single request after which a PHP backtrace will be dumped to the 'slowlog' file
request_slowlog_timeout = 10s

; The timeout for serving a single request after which the worker process will be killed
request_terminate_timeout = 300s

; Set open file descriptor rlimit
rlimit_files = 1024

; Set max core size rlimit
rlimit_core = 0

; Chroot to this directory at the start
;chroot = 

; Chdir to this directory at the start
; chdir = {{ app_root }}

; Redirect worker stdout and stderr into main error log
catch_workers_output = yes

; Clear environment in FPM workers
clear_env = no

; Ensure worker accepts environment variables
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; Additional php.ini defines, specific to this pool of workers
php_admin_value[sendmail_path] = /usr/sbin/sendmail -t -i -f <EMAIL>
php_flag[display_errors] = off
php_admin_value[error_log] = /var/log/php{{ php_version }}-fpm-worksuite.error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = {{ php_memory_limit }}
php_admin_value[max_execution_time] = {{ php_max_execution_time }}
php_admin_value[upload_max_filesize] = {{ php_upload_max_filesize }}
php_admin_value[post_max_size] = {{ php_post_max_size }}
