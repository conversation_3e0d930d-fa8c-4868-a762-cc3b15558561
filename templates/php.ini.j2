; WorkSuite SAAS PHP Configuration

; Memory and execution limits
memory_limit = {{ php_memory_limit }}
max_execution_time = {{ php_max_execution_time }}
max_input_time = {{ php_max_execution_time }}

; File upload settings
upload_max_filesize = {{ php_upload_max_filesize }}
post_max_size = {{ php_post_max_size }}
max_file_uploads = 20

; Session settings
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0
session.cookie_secure = 0
session.cookie_httponly = 1
session.save_handler = redis
session.save_path = "tcp://127.0.0.1:{{ redis_port }}?auth={{ redis_password }}"

; Error reporting
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log

; Date and timezone
date.timezone = {{ system_timezone }}

; Security settings
expose_php = Off
allow_url_fopen = On
allow_url_include = Off

; Performance settings
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; OPcache settings
opcache.enable = 1
opcache.enable_cli = 0
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.max_wasted_percentage = 5
opcache.use_cwd = 1
opcache.validate_timestamps = 0
opcache.revalidate_freq = 0
opcache.save_comments = 1
opcache.fast_shutdown = 1
opcache.enable_file_override = 0
opcache.optimization_level = 0x7FFFBFFF

; MySQL settings
mysqli.default_socket = /var/run/mysqld/mysqld.sock
pdo_mysql.default_socket = /var/run/mysqld/mysqld.sock
