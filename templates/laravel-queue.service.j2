[Unit]
Description=Lara<PERSON> Queue Worker for WorkSuite SAAS
After=network.target mariadb.service redis-server.service

[Service]
Type=simple
User={{ app_user }}
Group={{ app_group }}
WorkingDirectory={{ app_root }}
ExecStart=/usr/bin/php {{ app_root }}/artisan queue:work --sleep=3 --tries=3 --max-time=3600 --timeout=300
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=laravel-queue

# Resource limits
LimitNOFILE=65536
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths={{ app_root }}/storage {{ app_root }}/bootstrap/cache /var/log/worksuite

# Environment
Environment=HOME=/home/<USER>
Environment=USER={{ app_user }}

[Install]
WantedBy=multi-user.target
