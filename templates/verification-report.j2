WorkSuite SAAS Deployment Verification Report
Generated on: {{ ansible_date_time.iso8601 }}
Server: {{ inventory_hostname }} ({{ ansible_default_ipv4.address }})

=== SYSTEM INFORMATION ===
OS: {{ ansible_distribution }} {{ ansible_distribution_version }}
Kernel: {{ ansible_kernel }}
Architecture: {{ ansible_architecture }}
Memory: {{ ansible_memtotal_mb }}MB
CPU Cores: {{ ansible_processor_vcpus }}

=== SERVICE STATUS ===
{% for result in service_status.results %}
{{ result.item }}: {{ 'RUNNING' if result.status.ActiveState == 'active' else 'STOPPED' }}
{% endfor %}

=== NETWORK PORTS ===
{% for result in port_check.results %}
{{ result.item.name }} ({{ result.item.port }}): {{ 'LISTENING' if not result.failed else 'NOT LISTENING' }}
{% endfor %}

=== APPLICATION STATUS ===
Database Connection: {{ 'SUCCESS' if not db_test.failed else 'FAILED' }}
Redis Connection: {{ 'SUCCESS' if redis_test.stdout == 'PONG' else 'FAILED' }}
PHP-FPM Socket: {{ 'AVAILABLE' if php_socket.stat.exists else 'NOT AVAILABLE' }}

=== WEB ACCESS ===
HTTP ({{ nginx_http_port }}): {{ 'SUCCESS (Status: ' + http_test.status|string + ')' if not http_test.failed else 'FAILED' }}
HTTPS ({{ nginx_https_port }}): {{ 'SUCCESS (Status: ' + https_test.status|string + ')' if not https_test.failed else 'FAILED' }}

=== APPLICATION FILES ===
{% for result in app_files.results %}
{{ result.item | basename }}: {{ 'PRESENT' if result.stat.exists else 'MISSING' }}
{% endfor %}

=== PERMISSIONS ===
{% for result in app_perms.results %}
{{ result.item | basename }}: {{ result.stat.mode if result.stat.exists else 'N/A' }}
{% endfor %}

=== LARAVEL STATUS ===
{{ artisan_check.stdout if not artisan_check.failed else 'FAILED TO RUN ARTISAN' }}

=== LOG FILES ===
Total log files found: {{ log_files.files | length }}

=== ACCESS INFORMATION ===
HTTP URL: http://{{ ansible_default_ipv4.address }}:{{ nginx_http_port }}
HTTPS URL: https://{{ ansible_default_ipv4.address }}:{{ nginx_https_port }}

=== SSL CERTIFICATE ===
Certificate Path: {{ ssl_cert_path }}
Private Key Path: {{ ssl_key_path }}

=== DATABASE CREDENTIALS ===
Database: {{ mysql_database }}
Username: {{ mysql_user }}
Host: localhost:3306

=== REDIS CONFIGURATION ===
Host: localhost:{{ redis_port }}
Password: [CONFIGURED]

=== NEXT STEPS ===
1. Access the application via the URLs above
2. Complete the WorkSuite SAAS setup wizard if required
3. Configure email settings in the .env file
4. Set up regular backups using the provided backup script
5. Monitor logs in {{ app_storage }}/logs/ and /var/log/nginx/

=== TROUBLESHOOTING ===
- Check service logs: journalctl -u <service-name>
- Check Nginx logs: tail -f /var/log/nginx/worksuite-*.log
- Check PHP-FPM logs: tail -f /var/log/php{{ php_version }}-fpm-worksuite*.log
- Check application logs: tail -f {{ app_storage }}/logs/laravel.log
- Run backup manually: /usr/local/bin/backup-worksuite.sh
- SSL certificate info: /usr/local/bin/ssl-info.sh
