WorkSuite SAAS Deployment Information
=====================================

Deployment Date: {{ ansible_date_time.iso8601 }}
Server: {{ inventory_hostname }} ({{ ansible_default_ipv4.address }})
Deployed by: Ansible Automation

SYSTEM INFORMATION
------------------
OS: {{ ansible_distribution }} {{ ansible_distribution_version }}
Kernel: {{ ansible_kernel }}
Architecture: {{ ansible_architecture }}
Memory: {{ ansible_memtotal_mb }}MB
CPU Cores: {{ ansible_processor_vcpus }}

APPLICATION DETAILS
-------------------
Application: {{ app_name }}
Environment: {{ app_env }}
Root Directory: {{ app_root }}
Public Directory: {{ app_public }}
Storage Directory: {{ app_storage }}

WEB SERVER
----------
Server: Nginx
HTTP Port: {{ nginx_http_port }}
HTTPS Port: {{ nginx_https_port }}
Configuration: /etc/nginx/sites-available/worksuite-*

PHP CONFIGURATION
-----------------
Version: {{ php_version }}
Memory Limit: {{ php_memory_limit }}
Max Execution Time: {{ php_max_execution_time }}
Upload Max Size: {{ php_upload_max_filesize }}
Post Max Size: {{ php_post_max_size }}
FPM Socket: /run/php/php{{ php_version }}-fpm-worksuite.sock

DATABASE
--------
Type: MariaDB
Database: {{ mysql_database }}
Username: {{ mysql_user }}
Host: localhost
Port: 3306

CACHE & SESSIONS
----------------
Type: Redis
Host: localhost
Port: {{ redis_port }}
Password: [Configured]

SSL CERTIFICATE
---------------
Certificate: {{ ssl_cert_path }}
Private Key: {{ ssl_key_path }}
Type: Self-signed
Valid for: 365 days

SERVICES
--------
- nginx (Web Server)
- mariadb (Database)
- redis-server (Cache)
- php{{ php_version }}-fpm (PHP Processor)
- laravel-queue (Background Jobs)
- laravel-scheduler.timer (Scheduled Tasks)

ACCESS URLS
-----------
HTTP:  http://{{ ansible_default_ipv4.address }}:{{ nginx_http_port }}
HTTPS: https://{{ ansible_default_ipv4.address }}:{{ nginx_https_port }}

IMPORTANT FILES
---------------
Environment Config: {{ app_root }}/.env
Nginx HTTP Config: /etc/nginx/sites-available/worksuite-http
Nginx HTTPS Config: /etc/nginx/sites-available/worksuite-https
PHP-FPM Pool: /etc/php/{{ php_version }}/fpm/pool.d/worksuite.conf
MariaDB Config: /etc/mysql/mariadb.conf.d/99-worksuite.cnf
Redis Config: /etc/redis/redis.conf

BACKUP & MAINTENANCE
--------------------
Backup Script: /usr/local/bin/backup-worksuite.sh
Backup Directory: {{ backup_path }}
Backup Schedule: Daily at 2:00 AM
Retention: {{ backup_retention_days }} days

LOG FILES
---------
Application Logs: {{ app_storage }}/logs/
Nginx Access: /var/log/nginx/worksuite-access.log
Nginx Error: /var/log/nginx/worksuite-error.log
Nginx HTTPS Access: /var/log/nginx/worksuite-https-access.log
Nginx HTTPS Error: /var/log/nginx/worksuite-https-error.log
PHP-FPM: /var/log/php{{ php_version }}-fpm-worksuite*.log

USEFUL COMMANDS
---------------
# Check service status
systemctl status nginx
systemctl status mariadb
systemctl status redis-server
systemctl status php{{ php_version }}-fpm
systemctl status laravel-queue

# View logs
tail -f {{ app_storage }}/logs/laravel.log
tail -f /var/log/nginx/worksuite-error.log
journalctl -u laravel-queue -f

# Laravel commands
cd {{ app_root }}
php artisan --version
php artisan migrate:status
php artisan queue:work
php artisan schedule:run

# Backup
/usr/local/bin/backup-worksuite.sh

# SSL certificate info
/usr/local/bin/ssl-info.sh

SECURITY NOTES
--------------
- Change default passwords in production
- Configure proper firewall rules
- Use trusted SSL certificates for production
- Regular security updates
- Monitor access logs
- Set up intrusion detection

SUPPORT
-------
For issues, check:
1. Service status: systemctl status <service>
2. Service logs: journalctl -u <service>
3. Application logs: {{ app_storage }}/logs/
4. Nginx logs: /var/log/nginx/
5. Run verification: ansible-playbook verify.yml
