# Log rotation for WorkSuite SAAS
{{ app_root }}/storage/logs/*.log {
    daily
    missingok
    rotate {{ log_retention_days | default(30) }}
    compress
    delaycompress
    notifempty
    create 0644 {{ app_user }} {{ app_group }}
    postrotate
        systemctl reload php{{ php_version }}-fpm > /dev/null 2>&1 || true
    endscript
}

/var/log/worksuite/*.log {
    daily
    missingok
    rotate {{ log_retention_days | default(30) }}
    compress
    delaycompress
    notifempty
    create 0644 {{ app_user }} {{ app_group }}
}

/var/log/php{{ php_version }}-fpm-worksuite*.log {
    daily
    missingok
    rotate {{ log_retention_days | default(30) }}
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    postrotate
        systemctl reload php{{ php_version }}-fpm > /dev/null 2>&1 || true
    endscript
}
