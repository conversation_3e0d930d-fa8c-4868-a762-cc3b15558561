# WorkSuite SAAS Ansible Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying WorkSuite SAAS application on bare metal Ubuntu servers using Ansible automation. The deployment includes all necessary infrastructure components and application configuration.

## Prerequisites

### System Requirements
- **Target Server**: Ubuntu 20.04+ LTS
- **Control Machine**: Linux/macOS with Ansible installed
- **Memory**: Minimum 1GB RAM (2GB+ recommended)
- **Storage**: Minimum 10GB free space
- **Network**: SSH access to target server

### Required Software
- Ansible 2.9+
- SSH client
- Git (optional, for version control)

### Installation
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install ansible

# macOS
brew install ansible

# Verify installation
ansible --version
```

## Architecture

### Technology Stack
- **Web Server**: Nginx (HTTP: 8601, HTTPS: 8643)
- **Application**: PHP 8.2-FPM with Laravel Framework
- **Database**: MariaDB 10.11
- **Cache/Sessions**: Redis
- **SSL**: Self-signed certificates (production should use trusted CA)

### Directory Structure
```
/var/www/worksuite/          # Application root
├── public/                  # Web-accessible files
├── storage/                 # Logs, cache, uploads
├── bootstrap/cache/         # Framework cache
└── .env                     # Environment configuration

/etc/nginx/sites-available/  # Nginx virtual hosts
/etc/ssl/                    # SSL certificates
/var/log/                    # System and application logs
/var/backups/worksuite/      # Backup storage
```

## Quick Start

### 1. Prepare Environment
```bash
# Clone or download the deployment files
git clone <repository-url>
cd worksuite-ansible-deployment

# Ensure SSH key has correct permissions
chmod 400 thaodoiti.pem

# Test SSH connection
ssh -i thaodoiti.pem <EMAIL>
```

### 2. Configure Inventory
Edit `inventory/hosts.yml` to match your server details:
```yaml
worksuite-production:
  ansible_host: your-server-ip
  ansible_ssh_private_key_file: ./your-key.pem
  # Update other variables as needed
```

### 3. Run Deployment
```bash
# Complete deployment
./deploy.sh

# Or run individual components
ansible-playbook -i inventory/hosts.yml infrastructure.yml
ansible-playbook -i inventory/hosts.yml ssl-setup.yml
ansible-playbook -i inventory/hosts.yml application.yml
ansible-playbook -i inventory/hosts.yml verify.yml
```

### 4. Access Application
- **HTTP**: http://your-server:8601
- **HTTPS**: https://your-server:8643

## Deployment Components

### Infrastructure Setup (`infrastructure.yml`)
- System packages and updates
- PHP 8.2-FPM installation and configuration
- MariaDB installation and database setup
- Redis installation and configuration
- Nginx installation
- Firewall configuration
- System user creation

### SSL Configuration (`ssl-setup.yml`)
- Self-signed SSL certificate generation
- Nginx virtual host configuration
- HTTP and HTTPS site enablement

### Application Deployment (`application.yml`)
- Application file synchronization
- Composer dependency installation
- Environment configuration
- Database migrations
- Laravel optimization
- Background services setup
- Backup script installation

### Verification (`verify.yml`)
- Service status checks
- Port connectivity tests
- Database and Redis connectivity
- Application accessibility tests
- Comprehensive reporting

## Configuration

### Environment Variables
Key settings in `.env` file:
```bash
APP_NAME="WorkSuite SAAS"
APP_ENV=production
APP_DEBUG=false
APP_URL=http://your-server:8601

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_DATABASE=worksuite_saas
DB_USERNAME=worksuite_user
DB_PASSWORD=your-secure-password

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PORT=6379
```

### Security Configuration
- Database passwords are auto-generated
- Redis authentication disabled for simplicity
- UFW firewall configured for required ports
- SSL certificates for HTTPS encryption
- File permissions properly set

## Services Management

### System Services
```bash
# Check service status
sudo systemctl status nginx
sudo systemctl status mariadb
sudo systemctl status redis-server
sudo systemctl status php8.2-fpm
sudo systemctl status laravel-queue

# Restart services
sudo systemctl restart nginx
sudo systemctl restart mariadb
sudo systemctl restart redis-server
sudo systemctl restart php8.2-fpm
sudo systemctl restart laravel-queue

# View service logs
sudo journalctl -u nginx -f
sudo journalctl -u mariadb -f
sudo journalctl -u laravel-queue -f
```

### Application Commands
```bash
# Navigate to application directory
cd /var/www/worksuite

# Laravel commands (run as worksuite user)
sudo -u worksuite php artisan --version
sudo -u worksuite php artisan migrate:status
sudo -u worksuite php artisan queue:work
sudo -u worksuite php artisan cache:clear
sudo -u worksuite php artisan config:cache
```

## Monitoring and Logs

### Log Locations
- **Application**: `/var/www/worksuite/storage/logs/laravel.log`
- **Nginx Access**: `/var/log/nginx/worksuite-access.log`
- **Nginx Error**: `/var/log/nginx/worksuite-error.log`
- **PHP-FPM**: `/var/log/php8.2-fpm-worksuite*.log`
- **System**: `journalctl -u service-name`

### Monitoring Commands
```bash
# View application logs
sudo tail -f /var/www/worksuite/storage/logs/laravel.log

# View Nginx logs
sudo tail -f /var/log/nginx/worksuite-error.log

# Monitor system resources
htop
df -h
free -h

# Check port usage
sudo ss -tlnp | grep -E ':(8601|8643|3306|6379)'
```

## Backup and Maintenance

### Automated Backups
- **Script**: `/usr/local/bin/backup-worksuite.sh`
- **Schedule**: Daily at 2:00 AM via cron
- **Location**: `/var/backups/worksuite/`
- **Retention**: 7 days

### Manual Backup
```bash
# Run backup manually
sudo /usr/local/bin/backup-worksuite.sh

# List backups
ls -la /var/backups/worksuite/
```

### Database Maintenance
```bash
# Database backup
mysqldump -u worksuite_user -p worksuite_saas > backup.sql

# Database restore
mysql -u worksuite_user -p worksuite_saas < backup.sql

# Check database status
sudo mysql -u root -p -e "SHOW DATABASES;"
```

## Security Considerations

### Production Recommendations
1. **SSL Certificates**: Replace self-signed certificates with trusted CA certificates
2. **Passwords**: Change default database and Redis passwords
3. **Firewall**: Configure AWS Security Groups to allow only required ports
4. **Updates**: Enable automatic security updates
5. **Monitoring**: Set up log monitoring and alerting
6. **Backups**: Test backup and restore procedures regularly

### AWS Security Group Configuration
Required inbound rules:
- SSH (22): Your IP address
- HTTP (8601): 0.0.0.0/0 or specific IPs
- HTTPS (8643): 0.0.0.0/0 or specific IPs

## Performance Optimization

### PHP-FPM Tuning
Edit `/etc/php/8.2/fpm/pool.d/worksuite.conf`:
```ini
pm.max_children = 20
pm.start_servers = 4
pm.min_spare_servers = 2
pm.max_spare_servers = 8
```

### Nginx Optimization
- Gzip compression enabled
- Static file caching configured
- Buffer sizes optimized for application

### Database Optimization
- InnoDB buffer pool sized appropriately
- Query cache enabled
- Slow query logging enabled

## Next Steps

1. **Complete Application Setup**: Access the application and complete the WorkSuite SAAS setup wizard
2. **Configure Email**: Update SMTP settings in `.env` file
3. **Set Up Monitoring**: Implement monitoring and alerting
4. **SSL Certificates**: Install trusted SSL certificates for production
5. **Backup Testing**: Verify backup and restore procedures
6. **Performance Testing**: Load test the application
7. **Security Hardening**: Implement additional security measures

## Support

For issues and troubleshooting, refer to the TROUBLESHOOTING.md file or check:
- Application logs in `/var/www/worksuite/storage/logs/`
- System logs via `journalctl`
- Service status via `systemctl status`
